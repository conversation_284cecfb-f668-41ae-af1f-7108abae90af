"""
Tests for the MCP server functionality.
"""

import sys
from unittest.mock import AsyncMock, Mock, patch

import pytest

# Import to ensure module is loaded, then get actual module from sys.modules
from src.bible_study_mcp.server import (
    bible_study_method,
    class_content,
    get_class_summary,
    get_server,
    list_available_classes,
    method_overview,
    retrieve_class_content,
)

mcp_module = sys.modules["src.bible_study_mcp.server"]


class TestBibleStudyMCPServer:
    """Test cases for BibleStudyMCPServer functionality."""

    @pytest.fixture
    def mock_context_loader(self):
        """Create a mock context loader."""
        mock_loader = Mock()
        mock_loader.load_method_overview = AsyncMock(
            return_value="# 三三制查经方法\n\n测试内容"
        )
        mock_loader.list_available_classes.return_value = [1, 2, 3]
        mock_loader.get_class_summary.return_value = {
            "title": "测试标题",
            "description": "测试描述",
        }
        mock_loader.load_class_content = AsyncMock(return_value="测试课程内容")
        return mock_loader

    @pytest.mark.asyncio
    async def test_list_prompts(self):
        """Test that the server has prompts registered."""
        server = get_server()
        assert server is not None
        # FastMCP server should have prompt handlers
        assert hasattr(server, "_prompt_manager")

    @pytest.mark.asyncio
    async def test_get_prompt_bible_study_method(self, mock_context_loader):
        """Test getting the bible study method prompt."""
        with patch.object(mcp_module, "context_loader", mock_context_loader):
            result = await bible_study_method()

            assert "三三制查经方法" in result
            assert "nine-point framework" in result
            mock_context_loader.load_method_overview.assert_called_once_with()

    @pytest.mark.asyncio
    async def test_get_prompt_unknown(self):
        """Test getting an unknown prompt raises an error."""
        # FastMCP handles unknown prompts internally, so we test our
        # specific prompt function
        with patch.object(mcp_module, "context_loader") as mock_context_loader:
            mock_context_loader.load_method_overview.side_effect = FileNotFoundError(
                "File not found"
            )

            with pytest.raises(RuntimeError) as exc_info:
                await bible_study_method()
            assert "Method overview file not found" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_list_resources(self):
        """Test listing available resources."""
        server = get_server()
        assert server is not None
        # FastMCP server should have resource handlers
        assert hasattr(server, "_resource_manager")

    @pytest.mark.asyncio
    async def test_read_resource_method_overview(self, mock_context_loader):
        """Test reading the method overview resource."""
        with patch.object(mcp_module, "context_loader", mock_context_loader):
            result = await method_overview()

            assert result == "# 三三制查经方法\n\n测试内容"
            mock_context_loader.load_method_overview.assert_called_once_with()

    @pytest.mark.asyncio
    async def test_read_resource_class_content(self, mock_context_loader):
        """Test reading class content resource."""
        with patch.object(mcp_module, "context_loader", mock_context_loader):
            result = await class_content(1)

            assert result == "测试课程内容"
            mock_context_loader.load_class_content.assert_called_once_with(1)

    @pytest.mark.asyncio
    async def test_read_resource_unknown(self, mock_context_loader):
        """Test reading an unknown resource."""
        with patch.object(mcp_module, "context_loader", mock_context_loader):
            mock_context_loader.load_class_content.side_effect = FileNotFoundError(
                "File not found"
            )

            with pytest.raises(RuntimeError) as exc_info:
                await class_content(1)
            assert "Class file not found" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_list_tools(self):
        """Test listing available tools."""
        server = get_server()
        assert server is not None
        # FastMCP server should have tool handlers
        assert hasattr(server, "_tool_manager")

    @pytest.mark.asyncio
    async def test_call_tool_retrieve_class_content(self, mock_context_loader):
        """Test calling retrieve_class_content tool."""
        with patch.object(mcp_module, "context_loader", mock_context_loader):
            result = await retrieve_class_content(1)

            assert "第1讲: 测试标题" in result
            assert "测试描述" in result
            assert "测试课程内容" in result
            mock_context_loader.load_class_content.assert_called_once_with(1)
            mock_context_loader.get_class_summary.assert_called_once_with(1)

    @pytest.mark.asyncio
    async def test_call_tool_list_available_classes(self, mock_context_loader):
        """Test calling list_available_classes tool."""
        with patch.object(mcp_module, "context_loader", mock_context_loader):
            result = await list_available_classes()

            assert "Available Bible study classes" in result
            assert "1. 测试标题 - 测试描述" in result
            assert "2. 测试标题 - 测试描述" in result
            assert "3. 测试标题 - 测试描述" in result
            mock_context_loader.list_available_classes.assert_called_once_with()

    @pytest.mark.asyncio
    async def test_call_tool_get_class_summary(self, mock_context_loader):
        """Test calling get_class_summary tool."""
        with patch.object(mcp_module, "context_loader", mock_context_loader):
            result = await get_class_summary(1)

            assert "第1讲: 测试标题" in result
            assert "测试描述" in result
            mock_context_loader.get_class_summary.assert_called_once_with(1)

    @pytest.mark.asyncio
    async def test_call_tool_invalid_arguments(self, mock_context_loader):
        """Test calling tools with invalid arguments."""
        with patch.object(mcp_module, "context_loader", mock_context_loader):
            mock_context_loader.get_class_summary.side_effect = ValueError(
                "Invalid class number"
            )

            with pytest.raises(RuntimeError) as exc_info:
                await get_class_summary(999)
            assert "Invalid class number" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_call_tool_unknown_tool(self):
        """Test calling an unknown tool."""
        # FastMCP handles unknown tools internally, so we test that our
        # tools work correctly
        server = get_server()
        assert server is not None

    @pytest.mark.asyncio
    async def test_error_handling_in_handlers(self, mock_context_loader):
        """Test error handling in various handlers."""
        with patch.object(mcp_module, "context_loader", mock_context_loader):
            # Test error in prompt handler
            mock_context_loader.load_method_overview.side_effect = Exception(
                "General error"
            )

            with pytest.raises(RuntimeError) as exc_info:
                await bible_study_method()
            assert "Failed to load Bible study method overview" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_context_loader_caching_in_server(self, mock_context_loader):
        """Test that context loader caching works properly."""
        with patch.object(mcp_module, "context_loader", mock_context_loader):
            # Call method twice
            await bible_study_method()
            await bible_study_method()

            # Method should be called twice (no internal caching at server level)
            assert mock_context_loader.load_method_overview.call_count == 2

    @pytest.mark.asyncio
    async def test_file_not_found_error_handling(self, mock_context_loader):
        """Test proper handling of FileNotFoundError."""
        with patch.object(mcp_module, "context_loader", mock_context_loader):
            mock_context_loader.load_method_overview.side_effect = FileNotFoundError(
                "File not found"
            )

            with pytest.raises(RuntimeError) as exc_info:
                await bible_study_method()
            assert "Method overview file not found" in str(exc_info.value)
