"""
Tests for the context loading functionality.
"""

import asyncio
from pathlib import Path
from unittest.mock import Mock, mock_open, patch

import pytest
from docx import Document

from src.bible_study_mcp.context import ContextLoader


class TestContextLoader:
    """Test cases for ContextLoader class."""

    def test_init_with_default_documents_dir(self):
        """Test initialization with default documents directory."""
        with patch("pathlib.Path.exists", return_value=True):
            loader = ContextLoader()
            assert loader.documents_dir.name == "documents"

    def test_init_with_custom_documents_dir(self):
        """Test initialization with custom documents directory."""
        custom_dir = Path("/custom/documents")
        with patch("pathlib.Path.exists", return_value=True):
            loader = ContextLoader(custom_dir)
            assert loader.documents_dir == custom_dir

    def test_init_with_nonexistent_documents_dir(self):
        """Test initialization with non-existent documents directory."""
        with (
            patch("pathlib.Path.exists", return_value=False),
            patch("pathlib.Path.mkdir", side_effect=OSError("Permission denied")),
        ):
            with pytest.raises(
                FileNotFoundError,
                match="Documents directory not found and could not be created",
            ):
                ContextLoader()

    @pytest.mark.asyncio
    async def test_load_method_overview_success(self):
        """Test successful loading of method overview."""
        mock_content = "# 三三制查经方法\n\n这是测试内容。"

        with (
            patch("pathlib.Path.exists", return_value=True),
            patch("builtins.open", mock_open(read_data=mock_content)),
        ):
            loader = ContextLoader()
            content = await loader.load_method_overview()
            assert content == mock_content

    @pytest.mark.asyncio
    async def test_load_method_overview_file_not_found(self):
        """Test loading method overview when file doesn't exist."""
        with (
            patch("pathlib.Path.exists", side_effect=lambda: False),
            patch("pathlib.Path.mkdir", return_value=None),
        ):
            loader = ContextLoader()
            with pytest.raises(
                FileNotFoundError, match="Method overview file not found"
            ):
                await loader.load_method_overview()

    @pytest.mark.asyncio
    async def test_load_method_overview_encoding_fallback(self):
        """Test encoding fallback when UTF-8 fails."""
        mock_content = "# 三三制查经方法\n\n这是测试内容。"

        def mock_open_with_encoding_error(*args, **kwargs):
            if kwargs.get("encoding") == "utf-8":
                raise UnicodeDecodeError("utf-8", b"", 0, 1, "invalid start byte")
            elif kwargs.get("encoding") == "gbk":
                return mock_open(read_data=mock_content)(*args, **kwargs)
            else:
                raise UnicodeDecodeError("other", b"", 0, 1, "invalid start byte")

        with (
            patch("pathlib.Path.exists", return_value=True),
            patch("builtins.open", side_effect=mock_open_with_encoding_error),
        ):
            loader = ContextLoader()
            content = await loader.load_method_overview()
            assert content == mock_content

    @pytest.mark.asyncio
    async def test_load_class_content_success(self):
        """Test successful loading of class content."""
        mock_doc = Mock(spec=Document)
        mock_paragraph1 = Mock()
        mock_paragraph1.text = "第一段内容"
        mock_paragraph2 = Mock()
        mock_paragraph2.text = "第二段内容"
        mock_doc.paragraphs = [mock_paragraph1, mock_paragraph2]

        def mock_exists(self):
            path_str = str(self)
            # Prefer docx in this test
            if path_str.endswith("讲文字版.md"):
                return False
            if path_str.endswith("讲文字版.docx"):
                return True
            # Documents dir should exist
            if path_str.endswith("documents"):
                return True
            return False

        with (
            patch("pathlib.Path.exists", mock_exists),
            patch("src.bible_study_mcp.context.Document", return_value=mock_doc),
        ):
            loader = ContextLoader()
            content = await loader.load_class_content(1)
            assert content == "第一段内容\n\n第二段内容"

    @pytest.mark.asyncio
    async def test_load_class_content_invalid_class_number(self):
        """Test loading class content with invalid class number."""
        with patch("pathlib.Path.exists", return_value=True):
            loader = ContextLoader()

            with pytest.raises(
                ValueError, match="Class number must be between 1 and 10"
            ):
                await loader.load_class_content(0)

            with pytest.raises(
                ValueError, match="Class number must be between 1 and 10"
            ):
                await loader.load_class_content(11)

    @pytest.mark.asyncio
    async def test_load_class_content_file_not_found(self):
        """Test loading class content when file doesn't exist."""
        with (
            patch("pathlib.Path.exists", side_effect=lambda: False),
            patch("pathlib.Path.mkdir", return_value=None),
        ):
            loader = ContextLoader()
            with pytest.raises(FileNotFoundError, match="Class file not found"):
                await loader.load_class_content(1)

    def test_list_available_classes(self):
        """Test listing available classes."""
        # Create mock Path objects with proper name attributes
        mock_file1 = Mock()
        mock_file1.name = "1 第18期三三制第一讲文字版.docx"
        mock_file2 = Mock()
        mock_file2.name = "2 第18期三三制第二讲文字版.docx"
        mock_file3 = Mock()
        mock_file3.name = "10 第18期三三制第十讲文字版.docx"
        mock_file4 = Mock()
        mock_file4.name = "other_file.docx"  # Should be ignored

        mock_files = [mock_file1, mock_file2, mock_file3, mock_file4]

        with (
            patch("pathlib.Path.exists", return_value=True),
            patch("pathlib.Path.glob", return_value=mock_files),
        ):
            loader = ContextLoader()
            classes = loader.list_available_classes()
            assert classes == [1, 2, 10]

    def test_number_to_chinese(self):
        """Test number to Chinese conversion."""
        with patch("pathlib.Path.exists", return_value=True):
            loader = ContextLoader()

            assert loader._number_to_chinese(1) == "一"
            assert loader._number_to_chinese(5) == "五"
            assert loader._number_to_chinese(10) == "十"
            assert loader._number_to_chinese(11) == "11"  # Fallback for out of range

    def test_get_class_summary(self):
        """Test getting class summary."""
        with patch("pathlib.Path.exists", return_value=True):
            loader = ContextLoader()

            summary = loader.get_class_summary(1)
            assert summary["title"] == "三三制导论"
            assert "介绍三三制查经法" in summary["description"]

            summary = loader.get_class_summary(2)
            assert summary["title"] == "奇"
            assert "合神心意的义行" in summary["description"]

    def test_get_class_summary_invalid_number(self):
        """Test getting class summary with invalid number."""
        with patch("pathlib.Path.exists", return_value=True):
            loader = ContextLoader()

            with pytest.raises(
                ValueError, match="Class number must be between 1 and 10"
            ):
                loader.get_class_summary(0)

    @pytest.mark.asyncio
    async def test_load_docx_content_with_empty_paragraphs(self):
        """Test loading docx content with empty paragraphs."""
        mock_doc = Mock(spec=Document)
        mock_paragraph1 = Mock()
        mock_paragraph1.text = "有内容的段落"
        mock_paragraph2 = Mock()
        mock_paragraph2.text = "   "  # Whitespace only
        mock_paragraph3 = Mock()
        mock_paragraph3.text = ""  # Empty
        mock_paragraph4 = Mock()
        mock_paragraph4.text = "另一个有内容的段落"
        mock_doc.paragraphs = [
            mock_paragraph1,
            mock_paragraph2,
            mock_paragraph3,
            mock_paragraph4,
        ]

        def mock_exists(self):
            path_str = str(self)
            if path_str.endswith("讲文字版.md"):
                return False
            if path_str.endswith("讲文字版.docx"):
                return True
            if path_str.endswith("documents"):
                return True
            return False

        with (
            patch("pathlib.Path.exists", mock_exists),
            patch("src.bible_study_mcp.context.Document", return_value=mock_doc),
        ):
            loader = ContextLoader()
            content = await loader.load_class_content(1)
            assert content == "有内容的段落\n\n另一个有内容的段落"

    @pytest.mark.asyncio
    async def test_load_docx_content_error_handling(self):
        """Test error handling in docx content loading."""

        def mock_exists(self):
            path_str = str(self)
            if path_str.endswith("讲文字版.md"):
                return False
            if path_str.endswith("讲文字版.docx"):
                return True
            if path_str.endswith("documents"):
                return True
            return False

        with (
            patch("pathlib.Path.exists", mock_exists),
            patch(
                "src.bible_study_mcp.context.Document",
                side_effect=Exception("Docx error"),
            ),
        ):
            loader = ContextLoader()
            with pytest.raises(Exception, match="Docx error"):
                await loader.load_class_content(1)

    @pytest.mark.asyncio
    async def test_load_markdown_class_content_success(self):
        """Test successful loading of class content from markdown file."""
        mock_content = "# 第1讲\n\nMarkdown内容测试"

        def mock_exists(self):
            path_str = str(self)
            if path_str.endswith("documents") or "/documents/" in path_str:
                return True
            if path_str.endswith("讲文字版.md"):
                return True
            if path_str.endswith("讲文字版.docx"):
                return False
            return False

        with (
            patch("pathlib.Path.exists", mock_exists),
            patch("builtins.open", mock_open(read_data=mock_content)),
        ):
            loader = ContextLoader()
            content = await loader.load_class_content(1)
            assert "Markdown内容测试" in content

    @pytest.mark.asyncio
    async def test_load_markdown_class_content_encoding_fallback(self):
        """Test encoding fallback when loading markdown class content."""
        mock_content = "# 第1讲\n\n编码回退测试"

        def mock_open_with_encoding_error(*args, **kwargs):
            if kwargs.get("encoding") == "utf-8":
                raise UnicodeDecodeError("utf-8", b"", 0, 1, "invalid start byte")
            elif kwargs.get("encoding") == "gbk":
                return mock_open(read_data=mock_content)(*args, **kwargs)
            else:
                raise UnicodeDecodeError("other", b"", 0, 1, "invalid start byte")

        def mock_exists(self):
            path_str = str(self)
            if path_str.endswith("documents") or "/documents/" in path_str:
                return True
            if path_str.endswith("讲文字版.md"):
                return True
            if path_str.endswith("讲文字版.docx"):
                return False
            return False

        with (
            patch("pathlib.Path.exists", mock_exists),
            patch("builtins.open", side_effect=mock_open_with_encoding_error),
        ):
            loader = ContextLoader()
            content = await loader.load_class_content(1)
            assert "编码回退测试" in content

    @pytest.mark.asyncio
    async def test_caching_functionality(self):
        """Test that content is cached and reused."""
        mock_content = "# 三三制查经方法\n\n这是测试内容。"

        with (
            patch("pathlib.Path.exists", return_value=True),
            patch("builtins.open", mock_open(read_data=mock_content)) as mock_file,
        ):
            loader = ContextLoader(cache_ttl=60)  # 1 minute cache

            # First call should read from file
            content1 = await loader.load_method_overview()
            assert content1 == mock_content
            assert mock_file.call_count == 1

            # Second call should use cache
            content2 = await loader.load_method_overview()
            assert content2 == mock_content
            assert mock_file.call_count == 1  # Should not have called file again

    @pytest.mark.asyncio
    async def test_cache_expiration(self):
        """Test that cache expires after TTL."""
        mock_content = "# 三三制查经方法\n\n这是测试内容。"

        with (
            patch("pathlib.Path.exists", return_value=True),
            patch("builtins.open", mock_open(read_data=mock_content)) as mock_file,
        ):
            # Use a simpler approach - mock time.time to return specific values
            # First, let's manually control the cache by directly manipulating it
            loader = ContextLoader(cache_ttl=60)  # 1 minute cache

            # First call - should read from file
            with patch("time.time", return_value=0):
                content1 = await loader.load_method_overview()
                assert content1 == mock_content
                assert mock_file.call_count == 1

            # Second call - simulate time has passed beyond TTL
            with patch("time.time", return_value=61):  # 61 seconds later
                content2 = await loader.load_method_overview()
                assert content2 == mock_content
                assert mock_file.call_count == 2  # Should read from file again

    @pytest.mark.asyncio
    async def test_cancellation_support(self):
        """Test that operations can be cancelled."""
        with patch("pathlib.Path.exists", return_value=True):
            loader = ContextLoader()
            cancel_event = asyncio.Event()
            cancel_event.set()  # Cancel immediately

            with pytest.raises(asyncio.CancelledError, match="Operation cancelled"):
                await loader.load_method_overview(cancel_event=cancel_event)

    @pytest.mark.asyncio
    async def test_progress_reporting(self):
        """Test that progress is reported during operations."""
        mock_content = "# 三三制查经方法\n\n这是测试内容。"
        progress_calls = []

        def progress_callback(message):
            progress_calls.append(message)

        with (
            patch("pathlib.Path.exists", return_value=True),
            patch("builtins.open", mock_open(read_data=mock_content)),
        ):
            loader = ContextLoader()
            await loader.load_method_overview(progress_callback=progress_callback)

            assert len(progress_calls) >= 1
            assert any("Loading method overview" in call for call in progress_calls)

    @pytest.mark.asyncio
    async def test_progress_reporting_from_cache(self):
        """Test that progress is reported when loading from cache."""
        mock_content = "# 三三制查经方法\n\n这是测试内容。"
        progress_calls = []

        def progress_callback(message):
            progress_calls.append(message)

        with (
            patch("pathlib.Path.exists", return_value=True),
            patch("builtins.open", mock_open(read_data=mock_content)),
        ):
            loader = ContextLoader()

            # Load once to cache
            await loader.load_method_overview()

            # Load again with progress callback
            await loader.load_method_overview(progress_callback=progress_callback)

            assert len(progress_calls) >= 1
            assert any("loaded from cache" in call for call in progress_calls)

    def test_init_with_cache_ttl(self):
        """Test initialization with custom cache TTL."""
        with patch("pathlib.Path.exists", return_value=True):
            loader = ContextLoader(cache_ttl=300)  # 5 minutes
            assert loader.cache_ttl == 300

    def test_init_with_environment_variable(self):
        """Test initialization using environment variable for documents directory."""
        custom_path = "/custom/env/documents"

        # Mock os.environ.get to return the custom path for the specific
        # environment variable
        def mock_env_get(key, default=None):
            if key == "BIBLE_STUDY_DOCUMENTS_DIR":
                return custom_path
            return default

        with patch("os.environ.get", side_effect=mock_env_get):
            # Mock Path.exists to return True only for the custom path
            def mock_exists(self):
                return str(self) == custom_path

            with patch("pathlib.Path.exists", mock_exists):
                loader = ContextLoader()
                assert str(loader.documents_dir) == custom_path

    @pytest.mark.asyncio
    async def test_input_validation_with_string_class_number(self):
        """Test that string class numbers are properly rejected."""
        with patch("pathlib.Path.exists", return_value=True):
            loader = ContextLoader()

            with pytest.raises(
                ValueError, match="Class number must be an integer, got str"
            ):
                await loader.load_class_content("1")  # type: ignore

    def test_get_class_summary_with_string_class_number(self):
        """Test that string class numbers are properly rejected in get_class_summary."""
        with patch("pathlib.Path.exists", return_value=True):
            loader = ContextLoader()

            with pytest.raises(
                ValueError, match="Class number must be an integer, got str"
            ):
                loader.get_class_summary("1")  # type: ignore
