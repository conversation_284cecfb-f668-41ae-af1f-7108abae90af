"""
Tests for the chatbot service.

These tests focus on service-specific business logic like agent mode switching,
message processing, and response generation logic that can't be easily tested
through the API layer alone.
"""

from unittest.mock import patch

import pytest

from chat.models import (
    ChatCompletionRequest,
    Message,
    Role,
)
from chat.service import ChatbotService


class TestChatbotServiceLogic:
    """Test ChatbotService business logic."""

    @pytest.mark.asyncio
    async def test_agent_mode_routing(self):
        """Test that requests are routed to AgentService when agent mode is enabled."""
        request = ChatCompletionRequest(
            model="test-model",
            messages=[Message(role=Role.USER, content="Hello")],
        )

        # Test with agent mode enabled
        with (
            patch("chat.config.settings.use_agent", True),
            patch("chat.service.AgentService.generate_chat_completion") as mock_agent,
        ):
            mock_agent.return_value = "agent_response"

            result = await ChatbotService.generate_chat_completion(request)

            mock_agent.assert_called_once_with(request)
            assert result == "agent_response"

    @pytest.mark.asyncio
    async def test_message_processing_logic(self):
        """Test that messages are properly processed for LLM calls."""
        request = ChatCompletionRequest(
            model="test-model",
            messages=[
                Message(role=Role.SYSTEM, content="You are helpful"),
                Message(role=Role.USER, content="Hello"),
            ],
        )

        with (
            patch("chat.config.settings.use_agent", False),
            patch("chat.service.create_langchain_messages") as mock_create_messages,
        ):
            mock_create_messages.return_value = ["processed_messages"]

            # We'll let the test fail at the LLM call, but verify message processing
            try:
                await ChatbotService.generate_chat_completion(request)
            except Exception:
                pass  # Expected to fail at LLM call

            # Verify messages were processed correctly
            mock_create_messages.assert_called_once()
            call_args = mock_create_messages.call_args[0][0]
            assert len(call_args) == 2
            assert call_args[0]["role"] == "system"
            assert call_args[0]["content"] == "You are helpful"
            assert call_args[1]["role"] == "user"
            assert call_args[1]["content"] == "Hello"

    @pytest.mark.asyncio
    async def test_streaming_agent_mode_routing(self):
        """Test that streaming requests are routed to AgentService.

        When agent mode is enabled.
        """
        request = ChatCompletionRequest(
            model="test-model",
            messages=[Message(role=Role.USER, content="Hello")],
            stream=True,
        )

        async def mock_agent_stream():
            yield "chunk1"
            yield "chunk2"

        with (
            patch("chat.config.settings.use_agent", True),
            patch(
                "chat.service.AgentService.stream_chat_completion"
            ) as mock_agent_stream_method,
        ):
            mock_agent_stream_method.return_value = mock_agent_stream()

            chunks = []
            async for chunk in ChatbotService.stream_chat_completion(request):
                chunks.append(chunk)

            mock_agent_stream_method.assert_called_once_with(request)
            assert chunks == ["chunk1", "chunk2"]
