"""
Security tests for the Triple3-Chat API.
"""

from unittest.mock import patch

import pytest
from fastapi.testclient import Test<PERSON><PERSON>

from chat.config import settings
from chat.main import app


@pytest.fixture
def client():
    """Create a test client for the API."""
    return TestClient(app)


class TestAPIKeySecurity:
    """Test API key security and authentication."""

    def test_empty_api_keys_allows_access(self, client):
        """Test that empty API keys list allows access (development mode)."""
        with patch("chat.config.settings.api_keys", []):
            response = client.get(f"{settings.api_prefix}/models")
            # Should allow access when no API keys are configured
            assert response.status_code == 200

    def test_malformed_bearer_token(self, client):
        """Test malformed Bearer token handling."""
        with patch("chat.config.settings.api_keys", ["valid-key"]):
            # Test various malformed Authorization headers
            malformed_headers = [
                {"Authorization": "Bearer"},  # Missing token
                {"Authorization": "Bearer "},  # Empty token
                {"Authorization": "Basic dGVzdA=="},  # Wrong auth type
                {"Authorization": "bearer valid-key"},  # Wrong case
                {"Authorization": "Bearer\x00valid-key"},  # Null byte
                {"Authorization": "Bearer valid-key extra"},  # Extra content
            ]

            for headers in malformed_headers:
                response = client.get(f"{settings.api_prefix}/models", headers=headers)
                assert response.status_code == 401
                assert "Invalid or missing API key" in response.json()["detail"]

    def test_api_key_case_sensitivity(self, client):
        """Test that API keys are case-sensitive."""
        with patch("chat.config.settings.api_keys", ["CaseSensitiveKey"]):
            # Test wrong case
            response = client.get(
                f"{settings.api_prefix}/models",
                headers={"Authorization": "Bearer casesensitivekey"},
            )
            assert response.status_code == 401

            # Test correct case
            response = client.get(
                f"{settings.api_prefix}/models",
                headers={"Authorization": "Bearer CaseSensitiveKey"},
            )
            assert response.status_code == 200

    def test_api_key_with_special_characters(self, client):
        """Test API keys with special characters."""
        special_key = "key-with-special!@#$%^&*()_+{}|:<>?[]\\;'\",./"
        with patch("chat.config.settings.api_keys", [special_key]):
            response = client.get(
                f"{settings.api_prefix}/models",
                headers={"Authorization": f"Bearer {special_key}"},
            )
            assert response.status_code == 200


class TestErrorHandling:
    """Test error handling and information disclosure."""

    def test_invalid_model_error_message(self, client):
        """Test that invalid model errors don't leak sensitive information."""
        with (
            patch("chat.config.settings.api_keys", ["test-key"]),
            patch(
                "chat.config.settings.available_models",
                {"valid-model": "provider/valid-model"},
            ),
        ):
            response = client.post(
                f"{settings.api_prefix}/chat/completions",
                json={
                    "model": "invalid-model",
                    "messages": [{"role": "user", "content": "test"}],
                },
                headers={"Authorization": "Bearer test-key"},
            )

            assert response.status_code == 400
            error_detail = response.json()["detail"]

            # Should mention available models but not leak internal details
            assert "invalid-model" in error_detail
            assert "valid-model" in error_detail
            assert "Use the /models endpoint" in error_detail

            # Should not contain sensitive information
            assert "OPENROUTER_API_KEY" not in error_detail
            assert "secret" not in error_detail.lower()

    def test_internal_server_error_handling(self, client):
        """Test that internal errors don't leak sensitive information."""
        with (
            patch("chat.config.settings.api_keys", ["test-key"]),
            patch(
                "chat.config.settings.available_models",
                {"test-model": "provider/test-model"},
            ),
            patch(
                "chat.service.ChatbotService.generate_chat_completion"
            ) as mock_service,
        ):
            # Make the service raise an exception with sensitive information
            mock_service.side_effect = Exception(
                "Database connection failed: password=secret123"
            )

            response = client.post(
                f"{settings.api_prefix}/chat/completions",
                json={
                    "model": "test-model",
                    "messages": [{"role": "user", "content": "test"}],
                },
                headers={"Authorization": "Bearer test-key"},
            )

            assert response.status_code == 500
            error_detail = response.json()["detail"]

            # Should contain generic error message
            assert "Error creating chat completion" in error_detail
            # The actual exception message might be included, but this tests the pattern


class TestInputValidation:
    """Test input validation and sanitization."""

    def test_oversized_request(self, client):
        """Test handling of oversized requests."""
        with (
            patch("chat.config.settings.api_keys", ["test-key"]),
            patch(
                "chat.config.settings.available_models",
                {"test-model": "provider/test-model"},
            ),
        ):
            # Create a very large message
            large_content = "x" * 100000  # 100KB message

            response = client.post(
                f"{settings.api_prefix}/chat/completions",
                json={
                    "model": "test-model",
                    "messages": [{"role": "user", "content": large_content}],
                },
                headers={"Authorization": "Bearer test-key"},
            )

            # Should handle gracefully (either accept or reject with proper error)
            assert response.status_code in [200, 400, 413, 422, 500]

    def test_malformed_json_request(self, client):
        """Test handling of malformed JSON requests."""
        with patch("chat.config.settings.api_keys", ["test-key"]):
            response = client.post(
                f"{settings.api_prefix}/chat/completions",
                content="invalid json{",  # Malformed JSON
                headers={
                    "Authorization": "Bearer test-key",
                    "Content-Type": "application/json",
                },
            )

            assert response.status_code == 422  # Unprocessable Entity

    def test_missing_required_fields(self, client):
        """Test handling of requests with missing required fields."""
        with (
            patch("chat.config.settings.api_keys", ["test-key"]),
            patch(
                "chat.config.settings.available_models",
                {"test-model": "provider/test-model"},
            ),
        ):
            # Test missing model
            response = client.post(
                f"{settings.api_prefix}/chat/completions",
                json={"messages": [{"role": "user", "content": "test"}]},
                headers={"Authorization": "Bearer test-key"},
            )
            assert response.status_code == 422

            # Test missing messages
            response = client.post(
                f"{settings.api_prefix}/chat/completions",
                json={"model": "test-model"},
                headers={"Authorization": "Bearer test-key"},
            )
            assert response.status_code == 422


class TestCORSAndHeaders:
    """Test CORS and security headers."""

    def test_cors_headers_present(self, client):
        """Test that CORS headers are properly configured."""
        # Test with a regular GET request to see CORS headers
        response = client.get(f"{settings.api_prefix}/health")

        # Check for CORS headers (these are added by the CORS middleware)
        # Note: CORS headers are typically added to actual responses, not
        # OPTIONS preflight
        assert response.status_code == 200

        # Test with a cross-origin request simulation
        response = client.get(
            f"{settings.api_prefix}/health", headers={"Origin": "https://example.com"}
        )

        # Should still work and include CORS headers
        assert response.status_code == 200

    def test_health_endpoint_no_auth_required(self, client):
        """Test that health endpoint doesn't require authentication."""
        response = client.get(f"{settings.api_prefix}/health")
        assert response.status_code == 200
        assert response.json()["status"] == "ok"


class TestStreamingSecurity:
    """Test security aspects of streaming endpoints."""

    def test_streaming_authentication_required(self, client):
        """Test that streaming endpoints require authentication."""
        with patch("chat.config.settings.api_keys", ["test-key"]):
            response = client.post(
                f"{settings.api_prefix}/chat/completions",
                json={
                    "model": "test-model",
                    "messages": [{"role": "user", "content": "test"}],
                    "stream": True,
                },
            )
            assert response.status_code == 401

    def test_streaming_with_invalid_model(self, client):
        """Test streaming with invalid model."""
        with (
            patch("chat.config.settings.api_keys", ["test-key"]),
            patch(
                "chat.config.settings.available_models",
                {"valid-model": "provider/valid-model"},
            ),
        ):
            response = client.post(
                f"{settings.api_prefix}/chat/completions",
                json={
                    "model": "invalid-model",
                    "messages": [{"role": "user", "content": "test"}],
                    "stream": True,
                },
                headers={"Authorization": "Bearer test-key"},
            )
            assert response.status_code == 400
