"""
Tests for the configuration module.

Focus on testing configuration behavior rather than specific hardcoded values.
"""

import os
from unittest.mock import patch

from chat.config import Settings


def test_settings_basic_structure():
    """Test that settings have the expected structure and types."""
    settings = Settings()

    # Check that basic configuration properties exist and have correct types
    assert isinstance(settings.api_title, str)
    assert isinstance(settings.api_description, str)
    assert isinstance(settings.api_version, str)
    assert isinstance(settings.api_prefix, str)
    assert isinstance(settings.debug, bool)
    assert isinstance(settings.api_keys, list)
    assert isinstance(settings.openrouter_base_url, str)
    assert isinstance(settings.default_model, str)
    assert isinstance(settings.available_models, dict)
    assert isinstance(settings.system_message, str)
    assert isinstance(settings.max_tokens, int)
    assert isinstance(settings.temperature, float)
    assert isinstance(settings.rate_limit_requests, int)
    assert isinstance(settings.rate_limit_period, int)

    # Check that essential configurations are not empty
    assert len(settings.api_title) > 0
    assert len(settings.default_model) > 0
    assert len(settings.available_models) > 0
    assert len(settings.system_message) > 0


def test_environment_variable_override():
    """Test that environment variables properly override default values."""
    # Test key environment variable overrides
    env_vars = {
        "API_TITLE": "Test Environment API",
        "DEFAULT_MODEL": "test-env-model",
        "TEMPERATURE": "0.9",
        "MAX_TOKENS": "2048",
        "DEBUG": "true",
    }

    with patch.dict(os.environ, env_vars):
        settings = Settings()

        # Check that environment variables override defaults
        assert settings.api_title == "Test Environment API"
        assert settings.default_model == "test-env-model"
        assert settings.temperature == 0.9
        assert settings.max_tokens == 2048
        assert settings.debug is True


def test_api_keys_parsing():
    """Test that API keys are properly parsed from environment variables."""
    # Test JSON array parsing for API keys
    env_vars = {
        "API_KEYS": '["key1", "key2", "key3"]',
    }

    with patch.dict(os.environ, env_vars):
        settings = Settings()
        assert settings.api_keys == ["key1", "key2", "key3"]

    # Test empty API keys
    env_vars = {
        "API_KEYS": "[]",
    }

    with patch.dict(os.environ, env_vars):
        settings = Settings()
        assert settings.api_keys == []
