"""
Tests for the LLM integration.
"""

import os
from unittest.mock import MagicMock, patch

import pytest
from langchain_core.messages import AIMessage, HumanMessage, SystemMessage

from chat.llm import (
    convert_message_to_langchain,
    create_langchain_messages,
    get_available_models,
    get_chat_model,
)


def test_convert_message_to_langchain():
    """Test converting a message dict to a LangChain message."""
    # Test system message
    system_message = convert_message_to_langchain(
        {"role": "system", "content": "You are a helpful assistant."}
    )
    assert isinstance(system_message, SystemMessage)
    assert system_message.content == "You are a helpful assistant."

    # Test user message
    user_message = convert_message_to_langchain(
        {"role": "user", "content": "Hello, how are you?"}
    )
    assert isinstance(user_message, HumanMessage)
    assert user_message.content == "Hello, how are you?"

    # Test assistant message
    assistant_message = convert_message_to_langchain(
        {"role": "assistant", "content": "I'm doing well, thank you!"}
    )
    assert isinstance(assistant_message, AIMessage)
    assert assistant_message.content == "I'm doing well, thank you!"

    # Test invalid role
    with pytest.raises(ValueError):
        convert_message_to_langchain({"role": "invalid", "content": "Invalid role."})


def test_create_langchain_messages():
    """Test creating a list of LangChain messages."""
    # Test with system message
    messages = [
        {"role": "system", "content": "You are a helpful assistant."},
        {"role": "user", "content": "Hello, how are you?"},
    ]
    langchain_messages = create_langchain_messages(messages)
    assert len(langchain_messages) == 2
    assert isinstance(langchain_messages[0], SystemMessage)
    assert isinstance(langchain_messages[1], HumanMessage)

    # Test without system message
    messages = [
        {"role": "user", "content": "Hello, how are you?"},
    ]
    with patch("chat.config.settings.system_message", "Default system message."):
        langchain_messages = create_langchain_messages(messages)
        assert len(langchain_messages) == 2
        assert isinstance(langchain_messages[0], SystemMessage)
        assert langchain_messages[0].content == "Default system message."
        assert isinstance(langchain_messages[1], HumanMessage)


def test_get_available_models():
    """Test getting available models."""
    # Test with mocked settings
    with patch(
        "chat.config.settings.available_models",
        {
            "model1": "provider1/model1-id",
            "model2": "provider2/model2-id",
            "model3": "provider3/model3-id",
        },
    ):
        models = get_available_models()

        # Check that we get the expected number of models
        assert len(models) == 3

        # Check that the models have the expected structure
        assert "model1" in models
        assert models["model1"]["id"] == "provider1/model1-id"
        assert models["model1"]["provider"] == "provider1"
        assert models["model1"]["shorthand"] == "model1"
        assert models["model1"]["supports_streaming"] is True

        assert "model2" in models
        assert models["model2"]["provider"] == "provider2"

        assert "model3" in models
        assert models["model3"]["provider"] == "provider3"


@patch("chat.llm.ChatOpenAI")
def test_get_chat_model(mock_chat_openai):
    """Test getting a chat model."""
    # Mock the ChatOpenAI class
    mock_instance = MagicMock()
    mock_chat_openai.return_value = mock_instance

    # Test with default parameters
    env_vars = {"OPENAI_API_KEY": "test-api-key", "OPENROUTER_API_KEY": "test-api-key"}
    with (
        patch.dict(os.environ, env_vars),
        patch("chat.config.settings.openrouter_api_key", "test-api-key"),
        patch("chat.config.settings.openrouter_base_url", "https://test-url.com"),
        patch("chat.config.settings.default_model", "test-model"),
        patch("chat.config.settings.temperature", 0.7),
        patch("chat.config.settings.max_tokens", 1000),
    ):
        _, token_usage_handler = get_chat_model()

        # Check that ChatOpenAI was called with the correct parameters
        mock_chat_openai.assert_called_once()
        call_kwargs = mock_chat_openai.call_args.kwargs
        assert call_kwargs["base_url"] == "https://test-url.com"
        assert call_kwargs["model"] == "test-model"
        assert call_kwargs["temperature"] == 0.7
        assert (
            call_kwargs["max_completion_tokens"] == 1000
        )  # Make sure max_completion_tokens is passed

        # Check that the token usage handler is in the callbacks
        assert token_usage_handler in call_kwargs["callbacks"]

    # Test with custom parameters
    mock_chat_openai.reset_mock()
    env_vars = {"OPENAI_API_KEY": "test-api-key", "OPENROUTER_API_KEY": "test-api-key"}
    with (
        patch.dict(os.environ, env_vars),
        patch("chat.config.settings.openrouter_api_key", "test-api-key"),
        patch("chat.config.settings.openrouter_base_url", "https://test-url.com"),
        patch(
            "chat.config.settings.available_models", {"custom-model": "custom-model-id"}
        ),
    ):
        _, token_usage_handler = get_chat_model(
            model_name="custom-model",
            temperature=0.5,
            max_tokens=500,
        )

        # Check that ChatOpenAI was called with the correct parameters
        mock_chat_openai.assert_called_once()
        call_kwargs = mock_chat_openai.call_args.kwargs
        assert call_kwargs["base_url"] == "https://test-url.com"
        assert call_kwargs["model"] == "custom-model-id"
        assert call_kwargs["temperature"] == 0.5
        assert call_kwargs["max_completion_tokens"] == 500

    # Test with raw model name
    mock_chat_openai.reset_mock()
    with (
        patch.dict(os.environ, env_vars),
        patch("chat.config.settings.openrouter_api_key", "test-api-key"),
        patch("chat.config.settings.openrouter_base_url", "https://test-url.com"),
    ):
        _, token_usage_handler = get_chat_model(
            model_name="provider/raw-model-name",
            temperature=0.6,
            max_tokens=300,
        )

        # Check that ChatOpenAI was called with the correct parameters
        mock_chat_openai.assert_called_once()
        call_kwargs = mock_chat_openai.call_args.kwargs
        assert call_kwargs["base_url"] == "https://test-url.com"
        # Should use the raw model name directly
        assert call_kwargs["model"] == "provider/raw-model-name"
        assert call_kwargs["temperature"] == 0.6
        assert call_kwargs["max_completion_tokens"] == 300
