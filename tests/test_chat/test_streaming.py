"""
Tests for streaming functionality.
"""

from unittest.mock import patch

import pytest
from fastapi.testclient import TestClient

from chat.main import app
from chat.models import (
    ChatCompletionRequest,
    ChatCompletionStreamChoice,
    ChatCompletionStreamResponse,
    Delta,
    Message,
    Role,
)
from chat.service import ChatbotService


@pytest.fixture
def client():
    """Create a test client."""
    return TestClient(app)


@pytest.fixture
def sample_request():
    """Sample chat completion request."""
    return ChatCompletionRequest(
        model="gpt-4.1",
        messages=[Message(role=Role.USER, content="Hello, how are you?")],
        stream=True,
        temperature=0.7,
        max_tokens=100,
    )


@pytest.fixture
def sample_non_streaming_request():
    """Sample non-streaming chat completion request."""
    return ChatCompletionRequest(
        model="gpt-4.1",
        messages=[Message(role=Role.USER, content="Hello, how are you?")],
        stream=False,
        temperature=0.7,
        max_tokens=100,
    )


class TestStreamingService:
    """Test the streaming service functionality."""

    @pytest.mark.asyncio
    async def test_stream_chat_completion_basic(self, sample_request):
        """Test basic streaming functionality."""
        # Mock the AgentService.stream_chat_completion method
        mock_responses = [
            ChatCompletionStreamResponse(
                id="test-id",
                model="gpt-4.1",
                choices=[
                    ChatCompletionStreamChoice(
                        index=0,
                        delta=Delta(role=Role.ASSISTANT),
                        finish_reason=None,
                    )
                ],
            ),
            ChatCompletionStreamResponse(
                id="test-id",
                model="gpt-4.1",
                choices=[
                    ChatCompletionStreamChoice(
                        index=0, delta=Delta(content="Hello"), finish_reason=None
                    )
                ],
            ),
            ChatCompletionStreamResponse(
                id="test-id",
                model="gpt-4.1",
                choices=[
                    ChatCompletionStreamChoice(
                        index=0, delta=Delta(content=" there"), finish_reason=None
                    )
                ],
            ),
            ChatCompletionStreamResponse(
                id="test-id",
                model="gpt-4.1",
                choices=[
                    ChatCompletionStreamChoice(
                        index=0, delta=Delta(content="!"), finish_reason=None
                    )
                ],
            ),
            ChatCompletionStreamResponse(
                id="test-id",
                model="gpt-4.1",
                choices=[
                    ChatCompletionStreamChoice(
                        index=0, delta=Delta(), finish_reason="stop"
                    )
                ],
            ),
        ]

        async def mock_stream_generator():
            for response in mock_responses:
                yield response

        with patch(
            "chat.agent_service.AgentService.stream_chat_completion"
        ) as mock_stream:
            mock_stream.return_value = mock_stream_generator()

            # Collect all streaming responses
            responses = []
            async for response in ChatbotService.stream_chat_completion(sample_request):
                responses.append(response)

            # Should have initial chunk + content chunks + final chunk
            assert len(responses) == 5

            # Check initial response has role
            assert responses[0].choices[0].delta.role == Role.ASSISTANT
            assert responses[0].choices[0].finish_reason is None

            # Check content chunks
            content_responses = [
                r for r in responses[1:-1] if r.choices[0].delta.content
            ]
            assert len(content_responses) == 3  # "Hello", " there", "!"

            # Check final response has finish_reason
            assert responses[-1].choices[0].finish_reason == "stop"
            assert responses[-1].choices[0].delta.content is None

    @pytest.mark.asyncio
    async def test_stream_chat_completion_error_handling(self, sample_request):
        """Test error handling in streaming."""

        async def mock_error_generator():
            if False:  # Ensure this is a generator
                yield
            raise Exception("Test error")

        with patch(
            "chat.agent_service.AgentService.stream_chat_completion"
        ) as mock_stream:
            mock_stream.return_value = mock_error_generator()

            with pytest.raises(Exception, match="Test error"):
                async for _ in ChatbotService.stream_chat_completion(sample_request):
                    pass

    @pytest.mark.asyncio
    async def test_stream_chat_completion_empty_chunks(self, sample_request):
        """Test handling of empty chunks."""
        # Mock responses including some with empty content (should be filtered)
        mock_responses = [
            ChatCompletionStreamResponse(
                id="test-id",
                model="gpt-4.1",
                choices=[
                    ChatCompletionStreamChoice(
                        index=0,
                        delta=Delta(role=Role.ASSISTANT),
                        finish_reason=None,
                    )
                ],
            ),
            ChatCompletionStreamResponse(
                id="test-id",
                model="gpt-4.1",
                choices=[
                    ChatCompletionStreamChoice(
                        index=0, delta=Delta(content="Hello"), finish_reason=None
                    )
                ],
            ),
            ChatCompletionStreamResponse(
                id="test-id",
                model="gpt-4.1",
                choices=[
                    ChatCompletionStreamChoice(
                        index=0, delta=Delta(content=""), finish_reason=None
                    )
                ],
            ),
            ChatCompletionStreamResponse(
                id="test-id",
                model="gpt-4.1",
                choices=[
                    ChatCompletionStreamChoice(
                        index=0, delta=Delta(content=" world"), finish_reason=None
                    )
                ],
            ),
            ChatCompletionStreamResponse(
                id="test-id",
                model="gpt-4.1",
                choices=[
                    ChatCompletionStreamChoice(
                        index=0, delta=Delta(content=""), finish_reason=None
                    )
                ],
            ),
            ChatCompletionStreamResponse(
                id="test-id",
                model="gpt-4.1",
                choices=[
                    ChatCompletionStreamChoice(
                        index=0, delta=Delta(content="!"), finish_reason=None
                    )
                ],
            ),
            ChatCompletionStreamResponse(
                id="test-id",
                model="gpt-4.1",
                choices=[
                    ChatCompletionStreamChoice(
                        index=0, delta=Delta(), finish_reason="stop"
                    )
                ],
            ),
        ]

        async def mock_stream_generator():
            for response in mock_responses:
                yield response

        with patch(
            "chat.agent_service.AgentService.stream_chat_completion"
        ) as mock_stream:
            mock_stream.return_value = mock_stream_generator()

            responses = []
            async for response in ChatbotService.stream_chat_completion(sample_request):
                responses.append(response)

            # All responses should be returned (agent service would handle filtering)
            assert len(responses) == 7

            # Verify content (including empty ones, since that's what agent
            # service returns)
            content_responses = responses[1:-1]  # Exclude initial role and final stop
            contents = [r.choices[0].delta.content for r in content_responses]
            assert contents == ["Hello", "", " world", "", "!"]


class TestStreamingAPI:
    """Test the streaming API endpoint."""

    def test_streaming_endpoint_basic(self, client):
        """Test basic streaming endpoint functionality."""
        with (
            patch("chat.service.ChatbotService.stream_chat_completion") as mock_stream,
            patch("chat.config.settings.api_keys", ["test-key"]),
            patch(
                "chat.config.settings.available_models", {"gpt-4.1": "openai/gpt-4.1"}
            ),
        ):
            # Create mock streaming responses
            mock_responses = [
                ChatCompletionStreamResponse(
                    id="test-id",
                    model="gpt-4.1",
                    choices=[
                        ChatCompletionStreamChoice(
                            index=0,
                            delta=Delta(role=Role.ASSISTANT),
                            finish_reason=None,
                        )
                    ],
                ),
                ChatCompletionStreamResponse(
                    id="test-id",
                    model="gpt-4.1",
                    choices=[
                        ChatCompletionStreamChoice(
                            index=0, delta=Delta(content="Hello"), finish_reason=None
                        )
                    ],
                ),
                ChatCompletionStreamResponse(
                    id="test-id",
                    model="gpt-4.1",
                    choices=[
                        ChatCompletionStreamChoice(
                            index=0, delta=Delta(), finish_reason="stop"
                        )
                    ],
                ),
            ]

            async def mock_generator():
                for response in mock_responses:
                    yield response

            mock_stream.return_value = mock_generator()

            # Make streaming request
            response = client.post(
                "/api/v1/chat/completions",
                json={
                    "model": "gpt-4.1",
                    "messages": [{"role": "user", "content": "Hello"}],
                    "stream": True,
                },
                headers={"Authorization": "Bearer test-key"},
            )

            assert response.status_code == 200
            assert (
                response.headers["content-type"] == "text/event-stream; charset=utf-8"
            )

            # Parse the streaming response
            lines = response.text.strip().split("\n")
            data_lines = [line for line in lines if line.startswith("data: ")]

            # Should have data lines + [DONE]
            assert len(data_lines) >= 3
            assert data_lines[-1] == "data: [DONE]"

    def test_non_streaming_endpoint_unchanged(self, client):
        """Test that non-streaming requests still work."""
        with (
            patch(
                "chat.service.ChatbotService.generate_chat_completion"
            ) as mock_generate,
            patch("chat.config.settings.api_keys", ["test-key"]),
            patch(
                "chat.config.settings.available_models", {"gpt-4.1": "openai/gpt-4.1"}
            ),
        ):
            # Mock non-streaming response with proper ChatCompletionResponse
            from chat.models import (
                ChatCompletionChoice,
                ChatCompletionResponse,
                Message,
                Usage,
            )

            mock_response = ChatCompletionResponse(
                id="test-id",
                model="gpt-4.1",
                choices=[
                    ChatCompletionChoice(
                        index=0,
                        message=Message(role=Role.ASSISTANT, content="Hello there!"),
                        finish_reason="stop",
                    )
                ],
                usage=Usage(
                    prompt_tokens=10,
                    completion_tokens=5,
                    total_tokens=15,
                ),
            )
            mock_generate.return_value = mock_response

            response = client.post(
                "/api/v1/chat/completions",
                json={
                    "model": "gpt-4.1",
                    "messages": [{"role": "user", "content": "Hello"}],
                    "stream": False,
                },
                headers={"Authorization": "Bearer test-key"},
            )

            assert response.status_code == 200
            assert response.headers["content-type"] == "application/json"

            # Verify the mock was called
            mock_generate.assert_called_once()

    def test_streaming_error_handling(self, client):
        """Test error handling in streaming endpoint."""
        with (
            patch("chat.service.ChatbotService.stream_chat_completion") as mock_stream,
            patch("chat.config.settings.api_keys", ["test-key"]),
            patch(
                "chat.config.settings.available_models", {"gpt-4.1": "openai/gpt-4.1"}
            ),
        ):
            # Make streaming raise an exception
            async def mock_generator():
                if False:  # Ensure this is a generator
                    yield
                raise Exception("Streaming error")

            mock_stream.return_value = mock_generator()

            response = client.post(
                "/api/v1/chat/completions",
                json={
                    "model": "gpt-4.1",
                    "messages": [{"role": "user", "content": "Hello"}],
                    "stream": True,
                },
                headers={"Authorization": "Bearer test-key"},
            )

            assert response.status_code == 200  # SSE always returns 200

            # Check that error is in the response
            assert "error" in response.text or "Streaming error" in response.text
