"""
Tests for the chat.models module.

Focus on validation logic and edge cases rather than basic model creation.
"""

import pytest
from pydantic import ValidationError

from chat.models import (
    ChatCompletionRequest,
    Message,
    Role,
)


class TestMessageValidation:
    """Test Message model validation logic."""

    def test_invalid_role(self):
        """Test that invalid roles raise validation error."""
        with pytest.raises(ValidationError):
            # Use a literal that's not a valid Role enum value
            Message(role="invalid_role", content="Hello")  # type: ignore


class TestChatCompletionRequestValidation:
    """Test ChatCompletionRequest model validation and edge cases."""

    def test_temperature_edge_cases(self):
        """Test temperature validation edge cases."""
        messages = [Message(role=Role.USER, content="Hello")]

        # Test valid temperature values
        request = ChatCompletionRequest(
            model="test-model", messages=messages, temperature=0.5
        )
        assert request.temperature == 0.5

        # Test edge cases (negative temperature is allowed in this implementation)
        request_negative = ChatCompletionRequest(
            model="test-model", messages=messages, temperature=-1.0
        )
        assert request_negative.temperature == -1.0

    def test_empty_messages_allowed(self):
        """Test that empty messages list is allowed by the model."""
        # The current model allows empty messages - this tests the actual behavior
        request = ChatCompletionRequest(model="test-model", messages=[])
        assert request.messages == []

    def test_negative_max_tokens_allowed(self):
        """Test that negative max_tokens is allowed by the model."""
        messages = [Message(role=Role.USER, content="Hello")]

        # The current model allows negative max_tokens - this tests the actual behavior
        request = ChatCompletionRequest(
            model="test-model", messages=messages, max_tokens=-1
        )
        assert request.max_tokens == -1
