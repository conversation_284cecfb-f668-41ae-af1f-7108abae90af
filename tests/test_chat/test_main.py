"""
Tests for the chat.main module.

These tests focus on application-level concerns like middleware configuration,
route registration, and application assembly - distinct from endpoint functionality
which is tested in the API integration tests.
"""

import pytest
from fastapi.testclient import TestClient

from chat.main import app, create_app


class TestApplicationAssembly:
    """Test FastAPI application assembly and configuration."""

    @pytest.fixture
    def client(self):
        """Create test client for the main app."""
        return TestClient(app)

    def test_app_creation_and_configuration(self):
        """Test that the app is created with proper configuration."""
        test_app = create_app()

        # Test that app is properly configured (not just hardcoded values)
        assert test_app is not None
        assert hasattr(test_app, "title")
        assert hasattr(test_app, "description")
        assert hasattr(test_app, "version")
        assert test_app.docs_url is not None
        assert test_app.redoc_url is not None
        assert test_app.openapi_url is not None

    def test_cors_middleware_configuration(self, client):
        """Test that CORS middleware is properly configured."""
        # Test CORS headers on a request
        response = client.get(
            "/api/v1/health", headers={"Origin": "http://localhost:3000"}
        )
        assert response.status_code == 200

        # Verify CORS headers are present (critical for frontend integration)
        headers_lower = {k.lower(): v for k, v in response.headers.items()}
        assert "access-control-allow-origin" in headers_lower
        assert headers_lower["access-control-allow-origin"] == "*"

    def test_api_router_registration(self, client):
        """Test that API routes are properly registered in the main app."""
        # Test that core API endpoints are accessible through main app
        health_response = client.get("/api/v1/health")
        assert health_response.status_code == 200

        # Test that protected endpoints exist (even if they return 401)
        models_response = client.get("/api/v1/models")
        assert models_response.status_code in [200, 401]  # Should not be 404

        # Test that chat endpoint exists
        chat_response = client.post("/api/v1/chat/completions", json={})
        assert chat_response.status_code in [401, 422]  # Should not be 404

    def test_documentation_endpoints_configured(self):
        """Test that API documentation endpoints are properly configured."""
        test_app = create_app()

        # Test that documentation URLs are configured
        assert test_app.docs_url == "/api/v1/docs"
        assert test_app.redoc_url == "/api/v1/redoc"
        assert test_app.openapi_url == "/api/v1/openapi.json"

        # Test basic docs endpoint (avoid OpenAPI schema generation issues)
        client = TestClient(test_app)
        docs_response = client.get("/api/v1/docs")
        # Should either work (200) or have a schema issue (500),
        # but not be missing (404)
        assert docs_response.status_code != 404

    def test_global_exception_handler(self, client):
        """Test that global exception handler is configured."""
        # This is harder to test directly, but we can verify the handler exists
        test_app = create_app()
        assert len(test_app.exception_handlers) > 0

        # Verify that Exception is handled
        assert Exception in test_app.exception_handlers
