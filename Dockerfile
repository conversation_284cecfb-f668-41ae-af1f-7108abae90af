FROM python:3.10-slim

WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y --no-install-recommends \
    gcc \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# Install Poetry
RUN pip install --no-cache-dir poetry==2.0.1

# Copy necessary files first
COPY pyproject.toml poetry.lock README.md ./

# Copy the source code
COPY src/ ./src/

# Copy Bible study documents for MCP server
COPY documents/ ./documents/

# Copy scripts directory for MCP server functionality
COPY scripts/ ./scripts/

# Configure poetry to not use a virtual environment
RUN poetry config virtualenvs.create false

# Install dependencies including the project itself
RUN poetry install --no-interaction --no-ansi --without dev

# Create log directory
RUN mkdir -p /app/log

# Environment variables will be provided by Cloud Run
# Do not copy .env files into the container for security

# Set the entrypoint
ENV PORT=8080
CMD uvicorn chat.main:app --host 0.0.0.0 --port $PORT
