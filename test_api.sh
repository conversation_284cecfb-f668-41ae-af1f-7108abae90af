#!/bin/bash

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# API endpoint
API_URL="https://triple3-chat-7lcftxblja-uc.a.run.app"

# Check if jq is installed
if ! command -v jq &> /dev/null; then
    echo -e "${RED}Error: jq is not installed. Please install it to parse JSON responses.${NC}"
    echo "You can install it with: brew install jq on macOS or apt-get install jq on Ubuntu"
    exit 1
fi

# Function to test the health endpoint
test_health() {
    echo -e "${YELLOW}Testing health endpoint...${NC}"

    response=$(curl -s "${API_URL}/api/v1/health")

    if [ $? -ne 0 ]; then
        echo -e "${RED}Error: Failed to connect to the health endpoint.${NC}"
        exit 1
    fi

    echo -e "${GREEN}Health endpoint response:${NC}"
    echo "$response" | jq .
    echo ""
}

# Function to test the models endpoint
test_models() {
    echo -e "${YELLOW}Testing models endpoint...${NC}"

    response=$(curl -s -H "Authorization: Bearer $API_KEY" "${API_URL}/api/v1/models")

    if [ $? -ne 0 ]; then
        echo -e "${RED}Error: Failed to connect to the models endpoint.${NC}"
        return 1
    fi

    echo -e "${GREEN}Models endpoint response:${NC}"
    echo "$response" | jq .
    echo ""
}

# Function to test the chat completions endpoint (non-streaming)
test_chat_completions() {
    echo -e "${YELLOW}Testing chat completions endpoint (non-streaming)...${NC}"

    # Check if API key is provided
    if [ -z "$1" ]; then
        echo -e "${RED}Error: API key is required.${NC}"
        echo "Usage: $0 <api_key>"
        exit 1
    fi

    API_KEY="$1"

    # Create a request payload
    request_payload='{
        "model": "deepseek-v3",
        "messages": [
            {
                "role": "user",
                "content": "Hello, how are you today?"
            }
        ],
        "stream": false,
        "temperature": 0.7,
        "max_tokens": 100
    }'

    echo -e "${YELLOW}Request payload:${NC}"
    echo "$request_payload" | jq .
    echo ""

    # Send the request
    response=$(curl -s -X POST "${API_URL}/api/v1/chat/completions" \
        -H "Content-Type: application/json" \
        -H "Authorization: Bearer ${API_KEY}" \
        -d "$request_payload")

    if [ $? -ne 0 ]; then
        echo -e "${RED}Error: Failed to connect to the chat completions endpoint.${NC}"
        exit 1
    fi

    echo -e "${GREEN}Chat completions response:${NC}"
    echo "$response" | jq .
    echo ""
}

# Function to test the streaming chat completions endpoint
test_streaming_chat_completions() {
    echo -e "${YELLOW}Testing streaming chat completions endpoint...${NC}"

    # Check if API key is provided
    if [ -z "$1" ]; then
        echo -e "${RED}Error: API key is required.${NC}"
        echo "Usage: $0 <api_key>"
        exit 1
    fi

    API_KEY="$1"

    # Create a streaming request payload
    request_payload='{
        "model": "deepseek-v3",
        "messages": [
            {
                "role": "user",
                "content": "Count from 1 to 5"
            }
        ],
        "stream": true,
        "temperature": 0.7,
        "max_tokens": 50
    }'

    echo -e "${YELLOW}Streaming request payload:${NC}"
    echo "$request_payload" | jq .
    echo ""

    echo -e "${YELLOW}Streaming response (first 10 chunks):${NC}"

    # Send the streaming request and show first 10 chunks
    curl -s -X POST "${API_URL}/api/v1/chat/completions" \
        -H "Content-Type: application/json" \
        -H "Authorization: Bearer ${API_KEY}" \
        -d "$request_payload" \
        --no-buffer | head -20

    if [ $? -ne 0 ]; then
        echo -e "${RED}Error: Failed to connect to the streaming chat completions endpoint.${NC}"
        exit 1
    fi

    echo ""
    echo -e "${GREEN}Streaming test completed successfully!${NC}"
    echo ""
}

# Function to test Chinese Bible study questions with MCP agent
test_chinese_bible_study() {
    echo -e "${YELLOW}Testing Chinese Bible study questions with MCP agent...${NC}"

    # Check if API key is provided
    if [ -z "$1" ]; then
        echo -e "${RED}Error: API key is required.${NC}"
        echo "Usage: $0 <api_key>"
        exit 1
    fi

    API_KEY="$1"

    # Create a request payload with Chinese Bible study question
    request_payload='{
        "model": "gpt-4.1",
        "messages": [
            {
                "role": "user",
                "content": "三三制是什么？最后一课主要内容是什么"
            }
        ],
        "stream": false,
        "temperature": 0.7,
        "max_tokens": 500
    }'

    echo -e "${YELLOW}Chinese Bible study request payload:${NC}"
    echo "$request_payload" | jq .
    echo ""

    # Send the request
    response=$(curl -s -X POST "${API_URL}/api/v1/chat/completions" \
        -H "Content-Type: application/json" \
        -H "Authorization: Bearer ${API_KEY}" \
        -d "$request_payload")

    if [ $? -ne 0 ]; then
        echo -e "${RED}Error: Failed to connect to the chat completions endpoint.${NC}"
        exit 1
    fi

    echo -e "${GREEN}Chinese Bible study response:${NC}"
    echo "$response" | jq .
    echo ""
}

# Main function
main() {
    # Test health endpoint
    test_health

    # Get API key from command line argument
    API_KEY=$1

    if [ -z "$API_KEY" ]; then
        echo -e "${YELLOW}No API key provided. Skipping models, chat completions, and streaming tests.${NC}"
        echo "To test all endpoints (including streaming), run: ./test_api.sh <api_key>"
        exit 0
    fi

    # Test the models endpoint
    test_models

    # Test the chat completions endpoint (non-streaming)
    test_chat_completions "$1"

    # Test the streaming chat completions endpoint
    test_streaming_chat_completions "$1"

    # Test Chinese Bible study questions with MCP agent
    test_chinese_bible_study "$1"
}

# Run the main function
main "$@"
