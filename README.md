# Triple3 Chat

A multi-LLM chat API service with LangGraph agent integration and MCP server for Bible study methodology using the 三三制 (Triple-Three Method).

## Features

- **OpenAI-compatible REST API** with multi-LLM support via OpenRouter
- **Intelligent Bible Study Agent** with multi-step tool calling capabilities:
  - Retrieves specific class content from 10 三三制 lectures
  - Performs contextual analysis and cross-references
  - Provides detailed answers grounded in source materials
- **Model Context Protocol (MCP) server** providing 三三制 Bible study methodology
- **LangGraph ReAct agent** with persistent MCP session management
- **Streaming responses** using Server-Sent Events
- **Chinese text processing** with automatic encoding detection
- **Docker deployment** with Google Cloud Run support

## Prerequisites & Setup

### 1. System Requirements
- **Python 3.10+**
- **Poetry** for dependency management
- **OpenRouter API key** (required for LLM access)

### 2. OpenRouter API Key Setup
1. Sign up at [OpenRouter.ai](https://openrouter.ai/)
2. Create an API key in your OpenRouter dashboard
3. Copy the API key (format: `sk-or-v1-...`)

### 3. Environment Configuration
1. **Copy the environment template:**
   ```bash
   cp .env.example .env
   ```

2. **Edit the `.env` file with your configuration:**
   ```bash
   # OpenRouter configuration (REQUIRED)
   OPENROUTER_API_KEY="sk-or-v1-your-actual-openrouter-api-key-here"

   # Client authentication (REQUIRED)
   API_KEYS=["your-custom-api-key", "another-api-key"]

   # Service configuration
   USE_AGENT=true
   DEFAULT_MODEL="gpt-4.1"
   DEBUG=false

   # Optional: Advanced settings
   MAX_TOKENS=4000
   TEMPERATURE=0.7
   SYSTEM_MESSAGE="You are a helpful AI assistant."
   ```

3. **Key configuration notes:**
   - `OPENROUTER_API_KEY`: Your actual OpenRouter API key (starts with `sk-or-v1-`)
   - `API_KEYS`: JSON array of strings that clients use to authenticate with your service
   - `USE_AGENT`: Set to `true` to enable Bible study agent with MCP tools
   - Replace `"your-custom-api-key"` with your own chosen API key strings

### 4. Install Dependencies
```bash
poetry install
```

## Quick Start

After completing the setup above:

```bash
# 1. Start the main chat API service
poetry run uvicorn src.chat.main:app --host 0.0.0.0 --port 8000

# 2. (Optional) In another terminal, run MCP server for enhanced Bible study
poetry run python scripts/run_mcp_server.py

# 3. (Optional) Run interactive terminal chatbot
poetry run python scripts/terminal_chatbot.py

# 4. Test the service
./test_api.sh "your-custom-api-key"
```

The API will be available at:
- **Main API**: http://localhost:8000/api/v1/
- **Health check**: http://localhost:8000/health
- **API docs**: http://localhost:8000/api/v1/docs

## API Usage

The service provides OpenAI-compatible endpoints:

```bash
# Health check (no authentication required)
curl http://localhost:8000/health

# List available models (requires authentication)
curl -H "Authorization: Bearer your-custom-api-key" \
     http://localhost:8000/api/v1/models

# Chat completion (non-streaming)
curl -X POST http://localhost:8000/api/v1/chat/completions \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-custom-api-key" \
  -d '{
    "model": "claude-3.5-sonnet",
    "messages": [{"role": "user", "content": "Hello!"}],
    "stream": false
  }'

# Chat completion (streaming)
curl -X POST http://localhost:8000/api/v1/chat/completions \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-custom-api-key" \
  -d '{
    "model": "gpt-4o",
    "messages": [{"role": "user", "content": "Count from 1 to 5"}],
    "stream": true
  }'

# Bible study agent query (requires USE_AGENT=true)
curl -X POST http://localhost:8000/api/v1/chat/completions \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-custom-api-key" \
  -d '{
    "model": "claude-3.5-sonnet",
    "messages": [{"role": "user", "content": "请引用第6讲的具体内容来回答：父、主分别代表什么？"}],
    "stream": false
  }'
```

### Authentication
All endpoints except `/health` require HTTP Bearer authentication using one of the API keys configured in your `.env` file:
```
Authorization: Bearer your-custom-api-key
```

## Architecture

- **Chat API Service** (`src/chat/`): FastAPI-based REST API with LangGraph ReAct agent
  - Session-bound MCP tool integration for multi-step reasoning
  - Persistent tool calling throughout agent execution lifecycle
- **MCP Server** (`src/bible_study_mcp/`): Model Context Protocol server for Bible study
  - Provides 10 detailed lecture contents and summaries
  - Supports class-specific content retrieval and contextual queries
- **Study Materials** (`documents/`): 三三制 Bible study materials in Chinese
  - Complete lecture transcripts with automatic encoding detection
- **Scripts** (`scripts/`): Terminal chatbot, Gradio web interface, and utilities

## Configuration

### Environment Variables

**Required:**
- `OPENROUTER_API_KEY`: Your OpenRouter API key for LLM access (format: `sk-or-v1-...`)
- `API_KEYS`: JSON array of valid client API keys (e.g., `["key1", "key2"]`)

**Optional:**
- `USE_AGENT`: Enable LangGraph agent with MCP tools (default: `true`)
- `DEFAULT_MODEL`: Default model to use (default: `gpt-4.1`)
- `DEBUG`: Enable debug mode (default: `false`)
- `MAX_TOKENS`: Maximum tokens per response (default: `4000`)
- `TEMPERATURE`: Model temperature (default: `0.7`)
- `SYSTEM_MESSAGE`: Default system message
- `AGENT_MCP_SERVER_PATH`: Path to MCP server script (auto-detected if empty)

### Available Models
The service supports multiple LLM models via OpenRouter:
- `claude-3.5-sonnet` → `anthropic/claude-3.5-sonnet-20240620`
- `gpt-4o` → `openai/gpt-4o-2024-11-20`
- `gpt-4.1` → `openai/gpt-4.1`
- `gemini-2.5-pro` → `google/gemini-2.5-pro`
- Or use any OpenRouter model ID directly

## Troubleshooting

### Common Issues

**API Key Authentication Errors:**
- Ensure your `.env` file exists and has proper JSON format for `API_KEYS`
- Remove any comments from the `API_KEYS` line: `API_KEYS=["key1", "key2"]`
- Restart the service after modifying `.env`: `pkill -f uvicorn && poetry run uvicorn src.chat.main:app --host 0.0.0.0 --port 8000`

**OpenRouter Connection Issues:**
- Verify your `OPENROUTER_API_KEY` is valid and starts with `sk-or-v1-`
- Check your OpenRouter account has sufficient credits
- Test the key directly at OpenRouter.ai

**Service Won't Start:**
- Ensure Poetry dependencies are installed: `poetry install`
- Check if port 8000 is already in use: `lsof -i :8000`
- Verify Python 3.10+ is installed: `python --version`

### Testing
Use the included test script to verify everything works:
```bash
# Test basic functionality (no authentication required)
./test_api.sh

# Test full functionality (requires API key)
./test_api.sh "your-custom-api-key"
```

## Deployment

Deploy to Google Cloud Run:
```bash
./deploy.sh
```

## Documentation

For detailed development documentation, commands, and architecture overview, please refer to the source code comments and the Python files directly.
