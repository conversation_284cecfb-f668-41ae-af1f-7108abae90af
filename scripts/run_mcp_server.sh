#!/bin/bash

# Get the directory where this script is located
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# Get the project root directory (parent of examples)
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

# Change to project root directory
cd "$PROJECT_ROOT"

# Print debug information to stderr (will show in Claude Desktop logs)
echo "Script directory: $SCRIPT_DIR" >&2
echo "Project root: $PROJECT_ROOT" >&2
echo "Current working directory: $(pwd)" >&2
echo "Starting Bible Study MCP Server..." >&2
echo "" >&2

# Run the MCP server using Poetry
exec poetry run python examples/run_mcp_server.py
