#!/usr/bin/env python3
"""
Terminal-based chatbot using the local chat agent with MCP tools.

This script provides a simple command-line interface to interact with the
Bible study agent service locally without needing to run the full API server.
Uses the LangGraph agent with MCP tools for enhanced functionality.
"""

import asyncio
import sys
from pathlib import Path

# Add the src directory to Python path so we can import chat modules
src_path = Path(__file__).parent.parent / "src"
sys.path.insert(0, str(src_path))

from chat.agent_service import AgentService  # noqa: E402
from chat.config import settings  # noqa: E402
from chat.models import ChatCompletionRequest, Message, Role  # noqa: E402


class TerminalChatbot:
    """Terminal-based chatbot interface."""

    def __init__(self, model: str | None = None, streaming: bool = True):
        """Initialize the chatbot.

        Args:
            model: Model name to use (defaults to settings.default_model)
            streaming: Whether to use streaming responses
        """
        self.model = model or settings.default_model
        self.streaming = streaming
        self.conversation_history = []

        # Ensure we have an API key for OpenRouter
        if not settings.openrouter_api_key:
            print("⚠️  Warning: OPENROUTER_API_KEY not set in environment")
            print("   Set it in .env file or export OPENROUTER_API_KEY=your_key")
            print()

    def print_banner(self):
        """Print the chatbot banner."""
        print("🤖 Triple3 Chat Terminal Bot (Agent Mode)")
        print("=" * 50)
        print(f"Model: {self.model}")
        print(f"Streaming: {'Enabled' if self.streaming else 'Disabled'}")
        print("Agent: Bible Study MCP with tools")
        print("Type 'quit', 'exit', or Ctrl+C to exit")
        print("Type 'clear' to clear conversation history")
        print("Type 'models' to see available models")
        print("Type 'switch <model>' to change models")
        print("=" * 50)
        print()

    def print_available_models(self):
        """Print available models."""
        print("\n📋 Available models:")
        for shorthand, full_id in settings.available_models.items():
            current = " (current)" if shorthand == self.model else ""
            print(f"  • {shorthand} → {full_id}{current}")
        print()

    def switch_model(self, new_model: str):
        """Switch to a different model."""
        if new_model in settings.available_models:
            self.model = new_model
            print(f"✅ Switched to model: {new_model}")
        elif new_model in settings.available_models.values():
            # Allow switching by full model ID
            for shorthand, full_id in settings.available_models.items():
                if full_id == new_model:
                    self.model = shorthand
                    print(f"✅ Switched to model: {shorthand} ({full_id})")
                    return
        else:
            print(f"❌ Unknown model: {new_model}")
            self.print_available_models()

    async def get_response_streaming(self, request: ChatCompletionRequest):
        """Get a streaming response from the agent."""
        print("🤖 Agent: ", end="", flush=True)

        full_response = ""
        try:
            async for chunk in AgentService.stream_chat_completion(request):
                if chunk.choices and chunk.choices[0].delta.content:
                    content = chunk.choices[0].delta.content
                    print(content, end="", flush=True)
                    full_response += content
        except Exception as e:
            print(f"\n❌ Error: {e}")
            return None

        print()  # New line after response
        return full_response

    async def get_response_non_streaming(self, request: ChatCompletionRequest):
        """Get a non-streaming response from the agent."""
        print("🤖 Agent: ", end="", flush=True)

        try:
            response = await AgentService.generate_chat_completion(request)
            content = response.choices[0].message.content
            print(content)
            return content
        except Exception as e:
            print(f"❌ Error: {e}")
            return None

    async def chat_loop(self):
        """Main chat loop."""
        self.print_banner()

        while True:
            try:
                # Get user input
                user_input = input("👤 You: ").strip()

                if not user_input:
                    continue

                # Handle special commands
                if user_input.lower() in ["quit", "exit"]:
                    print("👋 Goodbye!")
                    break
                elif user_input.lower() == "clear":
                    self.conversation_history.clear()
                    print("🧹 Conversation history cleared")
                    continue
                elif user_input.lower() == "models":
                    self.print_available_models()
                    continue
                elif user_input.lower().startswith("switch "):
                    new_model = user_input[7:].strip()
                    self.switch_model(new_model)
                    continue

                # Add user message to history
                self.conversation_history.append(
                    Message(role=Role.USER, content=user_input)
                )

                # Create request
                request = ChatCompletionRequest(
                    model=self.model,
                    messages=self.conversation_history,
                    stream=self.streaming,
                    temperature=settings.temperature,
                    max_tokens=settings.max_tokens,
                )

                # Get response
                if self.streaming:
                    response_content = await self.get_response_streaming(request)
                else:
                    response_content = await self.get_response_non_streaming(request)

                # Add assistant response to history
                if response_content:
                    self.conversation_history.append(
                        Message(role=Role.ASSISTANT, content=response_content)
                    )

                print()  # Empty line for readability

            except KeyboardInterrupt:
                print("\n👋 Goodbye!")
                break
            except Exception as e:
                print(f"\n❌ Unexpected error: {e}")
                print("Continuing chat...\n")


def main():
    """Main entry point."""
    import argparse

    parser = argparse.ArgumentParser(
        description="Terminal chatbot using Triple3 Chat Agent with MCP tools"
    )
    parser.add_argument(
        "--model", "-m", help=f"Model to use (default: {settings.default_model})"
    )
    parser.add_argument(
        "--no-streaming", action="store_true", help="Disable streaming responses"
    )
    parser.add_argument(
        "--list-models", action="store_true", help="List available models and exit"
    )

    args = parser.parse_args()

    if args.list_models:
        print("📋 Available models:")
        for shorthand, full_id in settings.available_models.items():
            print(f"  • {shorthand} → {full_id}")
        return

    # Create and run chatbot
    chatbot = TerminalChatbot(model=args.model, streaming=not args.no_streaming)

    try:
        asyncio.run(chatbot.chat_loop())
    except KeyboardInterrupt:
        print("\n👋 Goodbye!")


if __name__ == "__main__":
    main()
