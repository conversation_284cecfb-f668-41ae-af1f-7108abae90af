#!/usr/bin/env python3
"""
Test script for Chinese language functionality with the MCP agent.
"""

import asyncio
import sys
from pathlib import Path

# Add the src directory to Python path so we can import chat modules
src_path = Path(__file__).parent.parent / "src"
sys.path.insert(0, str(src_path))

from chat.agent_service import AgentService  # noqa: E402
from chat.config import settings  # noqa: E402
from chat.models import ChatCompletionRequest, Message, Role  # noqa: E402


async def test_chinese_functionality():
    """Test Chinese language functionality with the MCP agent."""
    print("🧪 Testing Chinese functionality with MCP Agent")
    print("=" * 50)

    # Test messages in Chinese
    test_messages = [
        "你好，我想学习圣经。请介绍一下三三制学习法。",
        "请用中文解释三三制的针对、绝对、比对方法。",
        "能帮我分析马太福音5:3-4吗？请用三三制方法。",
    ]

    for i, chinese_message in enumerate(test_messages, 1):
        print(f"\n🔍 Test {i}: {chinese_message}")
        print("-" * 40)

        try:
            # Create message history
            messages = [Message(role=Role.USER, content=chinese_message)]

            # Create request
            request = ChatCompletionRequest(
                model=settings.default_model,
                messages=messages,
                stream=False,
                temperature=0.7,
                max_tokens=1000,
            )

            # Get response from agent
            print("🤖 Agent Response:")
            response = await AgentService.generate_chat_completion(request)

            if response and response.choices:
                content = response.choices[0].message.content
                print(content)
            else:
                print("❌ No response received")

        except Exception as e:
            print(f"❌ Error: {e}")
            import traceback

            traceback.print_exc()

        print("\n" + "=" * 50)


async def test_streaming_chinese():
    """Test streaming Chinese responses."""
    print("\n🌊 Testing Streaming Chinese Response")
    print("=" * 50)

    chinese_message = "请详细解释三三制圣经学习法的九个要点，并给出实际应用的例子。"
    print(f"📝 Question: {chinese_message}")
    print("\n🤖 Streaming Agent Response:")

    try:
        # Create message
        messages = [Message(role=Role.USER, content=chinese_message)]

        # Create streaming request
        request = ChatCompletionRequest(
            model=settings.default_model,
            messages=messages,
            stream=True,
            temperature=0.7,
            max_tokens=1500,
        )

        # Stream response
        full_response = ""
        async for chunk in AgentService.stream_chat_completion(request):
            if chunk.choices and chunk.choices[0].delta.content:
                content = chunk.choices[0].delta.content
                print(content, end="", flush=True)
                full_response += content

        print()  # New line after response
        print(f"\n✅ Total response length: {len(full_response)} characters")

    except Exception as e:
        print(f"❌ Streaming Error: {e}")
        import traceback

        traceback.print_exc()


async def main():
    """Main test function."""
    print("🚀 Starting Chinese Language Tests with MCP Agent")
    print(f"📊 Using model: {settings.default_model}")
    print(f"🔧 OpenRouter API Key configured: {bool(settings.openrouter_api_key)}")
    print()

    if not settings.openrouter_api_key:
        print("⚠️  Warning: OPENROUTER_API_KEY not set. Tests may fail.")
        print("   Set it in .env file or export OPENROUTER_API_KEY=your_key")
        print()

    # Test basic Chinese functionality
    await test_chinese_functionality()

    # Test streaming Chinese
    await test_streaming_chinese()

    print("\n🎉 Chinese language tests completed!")


if __name__ == "__main__":
    asyncio.run(main())
