"""
MCP (Model Context Protocol) server implementation for Bible study method context.

This module implements a standard MCP server that provides contextual information
about the 三三制 (Triple-Three Method) Bible study approach to chatbots.
"""

import logging
import sys
import time
from logging.handlers import RotatingFileHandler
from pathlib import Path

from mcp.server.fastmcp import FastMCP

# Handle imports for both module and standalone execution
try:
    from .context import ContextLoader
except ImportError:
    # If relative import fails, try absolute import
    sys.path.insert(0, str(Path(__file__).parent))
    try:
        from .context import ContextLoader
    except ImportError:
        # Last resort: try absolute import from the package
        from bible_study_mcp.context import ContextLoader

# Setup logging configuration
project_root = Path(__file__).parent.parent.parent
log_dir = project_root / "log"
log_dir.mkdir(exist_ok=True)

log_filename = f"bible_study_mcp_{time.strftime('%Y%m%d')}.log"
log_filepath = log_dir / log_filename

# Configure logging format
log_format = logging.Formatter("%(asctime)s - %(name)s - %(levelname)s - %(message)s")

# File handler with rotation
file_handler = RotatingFileHandler(
    filename=log_filepath,
    maxBytes=10 * 1024 * 1024,  # 10MB
    backupCount=5,
    encoding="utf-8",
)
file_handler.setLevel(logging.INFO)
file_handler.setFormatter(log_format)

# Console handler
console_handler = logging.StreamHandler()
console_handler.setLevel(logging.INFO)
console_handler.setFormatter(log_format)

# Initialize the MCP server
mcp = FastMCP("Bible Study MCP Server")

# Initialize context loader
context_loader = ContextLoader()

# Configure root logger at import time
root_logger = logging.getLogger()

# Remove existing handlers to avoid duplicates
for handler in root_logger.handlers[:]:
    root_logger.removeHandler(handler)

# Set level and add our handlers
root_logger.setLevel(logging.INFO)
root_logger.addHandler(file_handler)
root_logger.addHandler(console_handler)

# Create logger
logger = logging.getLogger(__name__)


@mcp.prompt()
async def bible_study_method() -> str:
    """三三制 (Triple-Three Method) Bible study approach overview."""
    try:
        overview_content = await context_loader.load_method_overview()
        return (
            f"You are a Bible study assistant specialized in the "
            f"三三制 (Triple-Three Method) approach.\n\n"
            f"Here is the complete method overview:\n\n"
            f"{overview_content}\n\n"
            f"Use this method to guide users in their Bible study, "
            f"helping them apply the nine-point framework: "
            f"针对 (Target), 绝对 (Absolute), and 比对 (Compare), "
            f"each with three sub-points. Always reference the specific "
            f"principles and techniques outlined in this method when "
            f"providing Bible study guidance."
        )
    except FileNotFoundError as e:
        logger.error(f"Method overview file not found: {e}")
        raise RuntimeError(f"Method overview file not found: {e}")
    except Exception as e:
        logger.error(f"Error loading method overview: {e}")
        raise RuntimeError(f"Failed to load Bible study method overview: {e}")


@mcp.resource("bible-study://method-overview")
async def method_overview() -> str:
    """Complete overview of the Triple-Three Method Bible study approach."""
    try:
        content = await context_loader.load_method_overview()
        return content
    except FileNotFoundError as e:
        logger.error(f"Method overview file not found: {e}")
        raise RuntimeError(f"Method overview file not found: {e}")
    except Exception as e:
        logger.error(f"Error reading method overview: {e}")
        raise RuntimeError(f"Failed to read method overview: {e}")


@mcp.resource("bible-study://class-{class_number}")
async def class_content(class_number: int) -> str:
    """Bible study class content."""
    try:
        content = await context_loader.load_class_content(class_number)
        return content
    except ValueError as e:
        logger.error(f"Invalid class number: {e}")
        raise RuntimeError(f"Invalid class number: {e}")
    except FileNotFoundError as e:
        logger.error(f"Class file not found: {e}")
        raise RuntimeError(f"Class file not found: {e}")
    except Exception as e:
        logger.error(f"Error reading class content: {e}")
        raise RuntimeError(f"Failed to read class content: {e}")


@mcp.tool()
async def retrieve_class_content(class_number: int) -> str:
    """Retrieve content for a specific Bible study class (1-10)."""
    try:
        content = await context_loader.load_class_content(class_number)
        class_summary = context_loader.get_class_summary(class_number)
        return (
            f"# 第{class_number}讲: {class_summary['title']}\n\n"
            f"{class_summary['description']}\n\n---\n\n{content}"
        )
    except Exception as e:
        logger.error(f"Error retrieving class content: {e}")
        raise RuntimeError(str(e))


@mcp.tool()
async def list_available_classes() -> str:
    """List all available Bible study class numbers."""
    try:
        available_classes = context_loader.list_available_classes()
        class_list = []
        for class_number in available_classes:
            summary = context_loader.get_class_summary(class_number)
            class_list.append(
                f"{class_number}. {summary['title']} - {summary['description']}"
            )
        return "Available Bible study classes:\n\n" + "\n".join(class_list)
    except Exception as e:
        logger.error(f"Error listing classes: {e}")
        raise RuntimeError(str(e))


@mcp.tool()
async def get_class_summary(class_number: int) -> str:
    """Get a summary of a specific Bible study class (1-10)."""
    try:
        summary = context_loader.get_class_summary(class_number)
        return f"第{class_number}讲: {summary['title']}\n\n{summary['description']}"
    except Exception as e:
        logger.error(f"Error getting class summary: {e}")
        raise RuntimeError(str(e))


def get_server() -> FastMCP:
    """Get the MCP server instance for testing."""
    return mcp


def main() -> None:
    """Main entry point for the MCP server."""
    logger.info("Starting Bible Study MCP server...")
    logger.info(f"Log file: {log_filepath}")
    mcp.run()


if __name__ == "__main__":
    main()
