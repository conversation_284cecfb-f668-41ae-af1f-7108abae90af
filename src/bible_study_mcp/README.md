# Bible Study MCP Server

This MCP (Model Context Protocol) server provides contextual information about the 三三制 (Triple-Three Method) Bible study approach to AI chatbots and other MCP clients.

## Overview

The 三三制 (Triple-Three Method) is a structured Bible study approach with a 9-point framework organized around three main categories:
- **针对 (Target)**: 针对人物、针对问题、针对应用
- **绝对 (Absolute)**: 绝对真理、绝对应许、绝对命令
- **比对 (Compare)**: 比对前后、比对平行、比对原则

This MCP server makes this methodology and related study materials available to AI assistants for enhanced Bible study conversations.

## Features

### Prompts
- **bible_study_method**: Provides comprehensive overview of the 三三制 methodology

### Resources
- **Method Overview**: Complete description of the Triple-Three Method approach
- **Class Content**: 10 detailed lessons covering different aspects of the methodology

### Tools
- **retrieve_class_content**: Get full content for a specific class (1-10)
- **list_available_classes**: List all available study classes
- **get_class_summary**: Get title and description for a specific class

## Installation

1. Install dependencies:
```bash
poetry install
```

2. Ensure you have the required documents in the `documents/` directory:
   - `0 三三制总结.md` - Method overview
   - `1-10 第18期三三制第X讲文字版.docx` - Class materials

## Usage

### Running the Server Locally

For development and testing, you can run the server directly:

```bash
# Using Poetry (recommended)
poetry run python examples/run_mcp_server.py

# Or using the shell script wrapper
./examples/run_mcp_server.sh
```

The server will start and listen for MCP protocol messages on stdin/stdout. You'll see output like:

```
Starting Bible Study MCP Server...
Server will provide context for 三三制 (Triple-Three Method) Bible study.
Press Ctrl+C to stop the server.
```

### Integration with AI Chatbots and MCP Clients

This MCP server can be integrated with any AI chatbot or application that supports the Model Context Protocol (MCP).

**Configuration:**

```json
{
  "mcpServers": {
    "bible-study": {
      "command": "/full/path/to/triple3-chat/examples/run_mcp_server.sh"
    }
  }
}
```

**Configuration Notes:**

- **Replace paths**: Update `/full/path/to/triple3-chat` with your actual project directory
- **Shell script permissions**: Ensure `run_mcp_server.sh` is executable (`chmod +x`)
- **Environment**: Make sure Poetry and Python dependencies are installed

**Testing the Integration:**

You can test the MCP server by running:

```bash
# Test server startup
./examples/run_mcp_server.sh
```

## API Reference

### Prompts

#### bible_study_method
Returns the complete 三三制 methodology overview for use as system context.

### Resources

#### bible-study://method-overview
- **Type**: text/markdown
- **Description**: Complete overview of the Triple-Three Method

#### bible-study://class-{number}
- **Type**: text/plain
- **Description**: Full content for class number (1-10)

### Tools

#### retrieve_class_content
Retrieve the complete content for a specific class.

**Parameters:**
- `class_number` (integer): Class number between 1 and 10

**Returns:** Full class content with title and description

#### list_available_classes
List all available study classes.

**Returns:** List of available class numbers with titles and descriptions

#### get_class_summary
Get summary information for a specific class.

**Parameters:**
- `class_number` (integer): Class number between 1 and 10

**Returns:** Class title and description

## Architecture

### Components

1. **ContextLoader** (`src/bible_study_mcp/context.py`)
   - Handles loading and parsing of study materials
   - Supports both Markdown (.md) and Word (.docx) formats
   - Implements Chinese character encoding fallback (UTF-8 → GBK → GB2312)

2. **BibleStudyMCPServer** (`src/bible_study_mcp/mcp.py`)
   - Implements the MCP protocol
   - Provides prompts, resources, and tools
   - Handles error cases and logging

### File Structure

```
src/bible_study_mcp/
├── __init__.py          # Package exports
├── context.py           # Document loading and processing
├── mcp.py              # MCP server implementation
└── README.md           # This file

documents/
├── 0 三三制总结.md      # Method overview
└── 1-10 第18期三三制第X讲文字版.docx  # Class materials

tests/test_bible_study_mcp/
├── __init__.py
├── test_context.py     # Context loader tests
├── test_mcp.py         # MCP server tests (basic)
└── test_integration.py # Integration tests

examples/
├── run_mcp_server.py   # Example server runner (Python)
└── run_mcp_server.sh   # Shell script wrapper for reliable startup
```

## Testing

Run the test suite:

```bash
# Run all tests
poetry run pytest tests/test_bible_study_mcp/ -v

# Run specific test files
poetry run pytest tests/test_bible_study_mcp/test_context.py -v
```

## Troubleshooting

### Common Issues

#### "Poetry could not find a pyproject.toml file"

This error occurs when Poetry runs from the wrong directory. Solutions:

1. **Use the shell script wrapper** (recommended):
   ```json
   {
     "mcpServers": {
       "bible-study": {
         "command": "/full/path/to/triple3-chat/examples/run_mcp_server.sh"
       }
     }
   }
   ```

2. **Ensure correct working directory** in your MCP client config:
   ```json
   {
     "mcpServers": {
       "bible-study": {
         "command": "poetry",
         "args": ["run", "python", "examples/run_mcp_server.py"],
         "cwd": "/full/path/to/triple3-chat"
       }
     }
   }
   ```

#### "ModuleNotFoundError: No module named 'docx'"

This indicates the virtual environment isn't being used. Make sure to:
- Use `poetry run` instead of running Python directly
- Use the provided shell script wrapper
- Ensure Poetry is installed and the project dependencies are installed with `poetry install`

#### Server Disconnects Immediately

Check the Claude Desktop logs for error messages. Common causes:
- Incorrect file paths in configuration
- Missing dependencies
- Permission issues with script files

#### Debug Information

The server provides debug output to stderr, which appears in Claude Desktop logs:
- Script directory location
- Project root directory
- Current working directory
- Python path information

Look for this information in your MCP client logs to verify the server is starting correctly.

## Error Handling

The server includes comprehensive error handling for:
- Missing or corrupted document files
- Invalid class numbers
- Encoding issues with Chinese characters
- MCP protocol errors

All errors are logged and returned as appropriate MCP error responses.

## Logging

The server logs to both stderr and `mcp_server.log` with information about:
- Server initialization
- Document loading
- Request handling
- Error conditions

## Contributing

1. Follow the existing code style and patterns
2. Add tests for new functionality
3. Update documentation as needed
4. Ensure Chinese character handling works correctly

## License

This project is part of the triple3-chat system for Bible study assistance.
