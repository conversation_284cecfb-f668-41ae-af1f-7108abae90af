"""
Context loading and processing module for Bible study method content.

This module provides functionality to load and process text files from the
documents directory, handling both .md and .docx file formats with proper
encoding for Chinese characters.
"""

import asyncio
import logging
import os
import re
import time
from collections.abc import Callable
from pathlib import Path

from docx import Document

logger = logging.getLogger(__name__)


class ContextLoader:
    """Loads and processes Bible study method context from documents."""

    def __init__(self, documents_dir: Path | None = None, cache_ttl: int = 3600):
        """
        Initialize the context loader.

        Args:
            documents_dir: Path to the documents directory. If None, uses the
                          default documents directory relative to the project root.
            cache_ttl: Time-to-live for cached content in seconds
                      (default: 3600 = 1 hour).
        """
        if documents_dir is None:
            # Try multiple possible locations for the documents directory
            possible_dirs = [
                # Current working directory
                Path.cwd() / "documents",
                # Project root (3 levels up from this file)
                Path(__file__).parent.parent.parent / "documents",
                # Parent of current directory
                Path.cwd().parent / "documents",
                # Environment variable if set
                (
                    Path(os.environ.get("BIBLE_STUDY_DOCUMENTS_DIR", ""))
                    if os.environ.get("BIBLE_STUDY_DOCUMENTS_DIR")
                    else None
                ),
            ]

            documents_dir = None
            for possible_dir in possible_dirs:
                if possible_dir and possible_dir.exists():
                    documents_dir = possible_dir
                    break

            if documents_dir is None:
                # Fall back to default location
                documents_dir = Path(__file__).parent.parent.parent / "documents"

        self.documents_dir = Path(documents_dir)
        if not self.documents_dir.exists():
            logger.warning(f"Documents directory not found: {self.documents_dir}")
            # Try to create the directory
            try:
                self.documents_dir.mkdir(parents=True, exist_ok=True)
                logger.info(f"Created documents directory: {self.documents_dir}")
            except Exception as e:
                raise FileNotFoundError(
                    f"Documents directory not found and could not be created: "
                    f"{self.documents_dir}. Error: {e}"
                )

        # Initialize cache
        self.cache_ttl = cache_ttl
        self._cache: dict[
            str, tuple[str, float]
        ] = {}  # {cache_key: (content, timestamp)}

        logger.info(
            f"Initialized ContextLoader with documents directory: {self.documents_dir}"
        )
        logger.info(f"Cache TTL: {cache_ttl} seconds")

    def _get_cache_key(self, file_type: str, identifier: str = "") -> str:
        """Generate a cache key for a given file type and identifier."""
        return f"{file_type}:{identifier}"

    def _is_cache_valid(self, cache_key: str) -> bool:
        """Check if cached content is still valid."""
        if cache_key not in self._cache:
            return False
        _, timestamp = self._cache[cache_key]
        return time.time() - timestamp < self.cache_ttl

    def _get_cached_content(self, cache_key: str) -> str | None:
        """Get cached content if valid."""
        if self._is_cache_valid(cache_key):
            content, _ = self._cache[cache_key]
            logger.debug(f"Cache hit for {cache_key}")
            return content
        return None

    def _cache_content(self, cache_key: str, content: str) -> None:
        """Cache content with current timestamp."""
        self._cache[cache_key] = (content, time.time())
        logger.debug(f"Cached content for {cache_key}")

    async def load_method_overview(
        self,
        cancel_event: asyncio.Event | None = None,
        progress_callback: Callable[[str], None] | None = None,
    ) -> str:
        """
        Load the complete Bible study method overview.

        Args:
            cancel_event: Optional event to signal cancellation.
            progress_callback: Optional callback to report progress.

        Returns:
            The content of the method overview as a string.

        Raises:
            FileNotFoundError: If the overview file is not found.
            UnicodeDecodeError: If there are encoding issues.
            asyncio.CancelledError: If the operation is cancelled.
        """
        # Check cache first
        cache_key = self._get_cache_key("overview")
        cached_content = self._get_cached_content(cache_key)
        if cached_content:
            if progress_callback:
                progress_callback("Method overview loaded from cache")
            return cached_content

        overview_file = self.documents_dir / "0 三三制总结.md"

        if not overview_file.exists():
            raise FileNotFoundError(f"Method overview file not found: {overview_file}")

        try:
            if cancel_event and cancel_event.is_set():
                raise asyncio.CancelledError("Operation cancelled")

            if progress_callback:
                progress_callback("Loading method overview file...")

            with open(overview_file, encoding="utf-8") as f:
                content = f.read()

            if progress_callback:
                progress_callback("Method overview loaded successfully")

            logger.info(f"Successfully loaded method overview from {overview_file}")

            # Cache the content
            self._cache_content(cache_key, content)

            return content

        except UnicodeDecodeError as e:
            logger.error(f"Encoding error reading {overview_file}: {e}")
            if progress_callback:
                progress_callback("Trying alternative encodings...")

            # Try with different encodings
            for encoding in ["gbk", "gb2312", "utf-8-sig"]:
                if cancel_event and cancel_event.is_set():
                    raise asyncio.CancelledError("Operation cancelled")

                if progress_callback:
                    progress_callback(f"Trying encoding: {encoding}")

                try:
                    with open(overview_file, encoding=encoding) as f:
                        content = f.read()

                    if progress_callback:
                        progress_callback(
                            f"Successfully loaded with {encoding} encoding"
                        )

                    logger.info(
                        f"Successfully loaded method overview using {encoding} encoding"
                    )

                    # Cache the content
                    self._cache_content(cache_key, content)

                    return content
                except UnicodeDecodeError:
                    continue

            raise UnicodeDecodeError(
                "utf-8",  # encoding (placeholder as we've tried multiple)
                bytes(str(overview_file), "utf-8"),  # object
                0,  # start
                len(str(overview_file)),  # end
                f"Could not decode {overview_file} with any supported "
                f"encoding",  # reason
            )

    async def load_class_content(
        self,
        class_number: int,
        cancel_event: asyncio.Event | None = None,
        progress_callback: Callable[[str], None] | None = None,
    ) -> str:
        """
        Load content for a specific class.

        Args:
            class_number: The class number (1-10).
            cancel_event: Optional event to signal cancellation.
            progress_callback: Optional callback to report progress.

        Returns:
            The content of the specified class as a string.

        Raises:
            ValueError: If class_number is not in valid range or not an integer.
            FileNotFoundError: If the class file is not found.
            asyncio.CancelledError: If the operation is cancelled.
        """
        if not isinstance(class_number, int):
            raise ValueError(
                f"Class number must be an integer, got {type(class_number).__name__}"
            )
        if not 1 <= class_number <= 10:
            raise ValueError(
                f"Class number must be between 1 and 10, got {class_number}"
            )

        # Check cache first
        cache_key = self._get_cache_key("class", str(class_number))
        cached_content = self._get_cached_content(cache_key)
        if cached_content:
            if progress_callback:
                progress_callback(f"Class {class_number} content loaded from cache")
            return cached_content

        # Try to find the class file, preferring Markdown then falling back to DOCX
        md_file = self.documents_dir / (
            f"{class_number} 第18期三三制第"
            f"{self._number_to_chinese(class_number)}讲文字版.md"
        )
        docx_file = self.documents_dir / (
            f"{class_number} 第18期三三制第"
            f"{self._number_to_chinese(class_number)}讲文字版.docx"
        )

        selected_file: Path | None = None
        selected_type: str | None = None

        if md_file.exists():
            selected_file = md_file
            selected_type = "md"
        elif docx_file.exists():
            selected_file = docx_file
            selected_type = "docx"
        else:
            raise FileNotFoundError(f"Class file not found: {md_file} or {docx_file}")

        try:
            if cancel_event and cancel_event.is_set():
                raise asyncio.CancelledError("Operation cancelled")

            if progress_callback:
                progress_callback(f"Loading class {class_number} content...")

            if selected_type == "md":
                # Load Markdown content with encoding fallbacks
                content = await self._load_markdown_content(
                    selected_file, cancel_event, progress_callback
                )
            else:
                # Load DOCX content
                content = await self._load_docx_content(
                    selected_file, cancel_event, progress_callback
                )

            if progress_callback:
                progress_callback(f"Class {class_number} content loaded successfully")

            logger.info(
                f"Successfully loaded class {class_number} content from {selected_file}"
            )

            # Cache the content
            self._cache_content(cache_key, content)

            return content

        except Exception as e:
            logger.error(f"Error loading class {class_number} content: {e}")
            raise

    def list_available_classes(self) -> list[int]:
        """
        List all available class numbers.

        Returns:
            A sorted list of available class numbers.
        """
        available_classes: list[int] = []
        seen: set[int] = set()

        # Look for numbered files in the documents directory (.md and .docx)
        # Markdown files
        for file_path in self.documents_dir.glob("*.md"):
            match_md = re.match(r"(\d+)\s+第18期三三制第.+讲文字版\.md", file_path.name)
            if match_md:
                class_number = int(match_md.group(1))
                if 1 <= class_number <= 10 and class_number not in seen:
                    available_classes.append(class_number)
                    seen.add(class_number)
        # DOCX files
        for file_path in self.documents_dir.glob("*.docx"):
            match_docx = re.match(
                r"(\d+)\s+第18期三三制第.+讲文字版\.docx", file_path.name
            )
            if match_docx:
                class_number = int(match_docx.group(1))
                if 1 <= class_number <= 10 and class_number not in seen:
                    available_classes.append(class_number)
                    seen.add(class_number)

        available_classes.sort()
        logger.info(
            f"Found {len(available_classes)} available classes: {available_classes}"
        )
        return available_classes

    async def _load_docx_content(
        self,
        file_path: Path,
        cancel_event: asyncio.Event | None = None,
        progress_callback: Callable[[str], None] | None = None,
    ) -> str:
        """
        Load content from a .docx file.

        Args:
            file_path: Path to the .docx file.
            cancel_event: Optional event to signal cancellation.
            progress_callback: Optional callback to report progress.

        Returns:
            The text content of the document.

        Raises:
            asyncio.CancelledError: If the operation is cancelled.
        """
        try:
            if cancel_event and cancel_event.is_set():
                raise asyncio.CancelledError("Operation cancelled")

            if progress_callback:
                progress_callback("Opening document...")

            doc = Document(str(file_path))
            paragraphs = []
            total_paragraphs = len(doc.paragraphs)

            if progress_callback:
                progress_callback(f"Processing {total_paragraphs} paragraphs...")

            for i, paragraph in enumerate(doc.paragraphs):
                if cancel_event and cancel_event.is_set():
                    raise asyncio.CancelledError("Operation cancelled")

                if (
                    progress_callback and i % 10 == 0
                ):  # Report progress every 10 paragraphs
                    progress_callback(f"Processing paragraph {i+1}/{total_paragraphs}")

                text = paragraph.text.strip()
                if text:  # Only add non-empty paragraphs
                    paragraphs.append(text)

            if progress_callback:
                progress_callback("Finalizing document content...")

            content = "\n\n".join(paragraphs)
            return content

        except Exception as e:
            logger.error(f"Error reading .docx file {file_path}: {e}")
            raise

    async def _load_markdown_content(
        self,
        file_path: Path,
        cancel_event: asyncio.Event | None = None,
        progress_callback: Callable[[str], None] | None = None,
    ) -> str:
        """
        Load content from a Markdown (.md) file with encoding fallbacks.

        Args:
            file_path: Path to the .md file.
            cancel_event: Optional event to signal cancellation.
            progress_callback: Optional callback to report progress.

        Returns:
            The text content of the Markdown file.

        Raises:
            asyncio.CancelledError: If the operation is cancelled.
            UnicodeDecodeError: If unable to decode with supported encodings.
        """
        if cancel_event and cancel_event.is_set():
            raise asyncio.CancelledError("Operation cancelled")

        if progress_callback:
            progress_callback("Opening markdown file...")

        try:
            with open(file_path, encoding="utf-8") as f:
                return f.read()
        except UnicodeDecodeError:
            # Try alternative encodings
            for encoding in ["gbk", "gb2312", "utf-8-sig"]:
                if cancel_event and cancel_event.is_set():
                    raise asyncio.CancelledError("Operation cancelled")
                try:
                    with open(file_path, encoding=encoding) as f:
                        if progress_callback:
                            progress_callback(
                                f"Successfully loaded with {encoding} encoding"
                            )
                        return f.read()
                except UnicodeDecodeError:
                    continue
            raise

    def _number_to_chinese(self, number: int) -> str:
        """
        Convert a number to Chinese numeral.

        Args:
            number: The number to convert (1-10).

        Returns:
            The Chinese numeral as a string.
        """
        chinese_numbers = {
            1: "一",
            2: "二",
            3: "三",
            4: "四",
            5: "五",
            6: "六",
            7: "七",
            8: "八",
            9: "九",
            10: "十",
        }
        return chinese_numbers.get(number, str(number))

    def get_class_summary(self, class_number: int) -> dict[str, str]:
        """
        Get a summary of a specific class including title and brief description.

        Args:
            class_number: The class number (1-10).

        Returns:
            A dictionary containing class title and description.

        Raises:
            ValueError: If class_number is not in valid range or not an integer.
        """
        if not isinstance(class_number, int):
            raise ValueError(
                f"Class number must be an integer, got {type(class_number).__name__}"
            )
        if not 1 <= class_number <= 10:
            raise ValueError(
                f"Class number must be between 1 and 10, got {class_number}"
            )

        # Class titles and descriptions based on the overview
        class_info = {
            1: {
                "title": "三三制导论",
                "description": "介绍三三制查经法的起源、意义和总体结构",
            },
            2: {
                "title": "奇",
                "description": "合神心意的义行，从正面行为中理解属灵美德",
            },
            3: {"title": "反", "description": "识别悖逆神的言行，是对罪性的揭示"},
            4: {"title": "辨", "description": "包括字义辨、语境辨、范式辨三个层面"},
            5: {"title": "真", "description": "神的启示真相，是不受时空限制的属灵事实"},
            6: {
                "title": "父、主",
                "description": "父代表神的心意与命令，"
                "主代表在基督里已经预备的恩典与资源",
            },
            7: {
                "title": "己",
                "description": "与我何干，以正己。带领学员自我归正与反思生命状态",
            },
            8: {
                "title": "境",
                "description": "处境关联，以免疫。寻找今人与古人在属灵处境中的相似性",
            },
            9: {
                "title": "髓（上）",
                "description": "通过举一反三，以通理引导学员将经文真理升华为属灵原则",
            },
            10: {
                "title": "髓（下）与整合",
                "description": "以仿神学为视角说明如何将圣经原则应用于各领域",
            },
        }

        return class_info.get(
            class_number, {"title": f"第{class_number}讲", "description": "课程内容"}
        )
