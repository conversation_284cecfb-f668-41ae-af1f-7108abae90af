"""
LangGraph agent service implementation with proper MCP tools integration.

This module implements a LangGraph ReAct agent that connects to the Bible study
MCP server and uses its tools, prompts, and resources following MCP best practices.
"""

import logging
import sys
import uuid
from collections.abc import AsyncGenerator
from pathlib import Path
from typing import Any

from langchain_core.runnables import RunnableConfig
from langchain_mcp_adapters.client import MultiServerMCPClient
from langchain_mcp_adapters.tools import load_mcp_tools
from langgraph.checkpoint.memory import MemorySaver
from langgraph.prebuilt import create_react_agent

from chat.config import settings
from chat.llm import get_chat_model
from chat.models import (
    ChatCompletionChoice,
    ChatCompletionRequest,
    ChatCompletionResponse,
    ChatCompletionStreamChoice,
    ChatCompletionStreamResponse,
    Delta,
    Message,
    Role,
    Usage,
)

logger = logging.getLogger(__name__)


class AgentService:
    """Service for handling LangGraph agent interactions with MCP tools."""

    def __init__(self):
        self._agent = None
        self._checkpointer = MemorySaver()
        self._mcp_client = None
        self._tools = None
        self._system_prompt = None

    async def _get_mcp_server_path(self) -> str:
        """Get the path to the MCP server script."""
        if (
            settings.agent_mcp_server_path
            and Path(settings.agent_mcp_server_path).exists()
        ):
            return settings.agent_mcp_server_path

        # Auto-detect MCP server path
        project_root = Path(__file__).parent.parent.parent
        mcp_server_path = project_root / "src" / "bible_study_mcp" / "server.py"

        if not mcp_server_path.exists():
            raise FileNotFoundError(f"MCP server not found at {mcp_server_path}")

        return str(mcp_server_path)

    async def _initialize_mcp_client(self):
        """Initialize MCP client connection to Bible study server."""
        if self._mcp_client is not None:
            return self._mcp_client

        try:
            # Get path to MCP server
            server_path = await self._get_mcp_server_path()

            # Use MultiServerMCPClient with stdio transport
            self._mcp_client = MultiServerMCPClient(
                {
                    "bible-study": {
                        "command": sys.executable,
                        "args": [server_path],
                        "transport": "stdio",
                    }
                }
            )

            logger.info(
                f"Initialized MCP client for Bible study server at {server_path}"
            )
            return self._mcp_client

        except Exception as e:
            logger.error(f"Failed to initialize MCP client: {e}")
            raise

    async def _load_mcp_tools(self, session: Any | None = None):
        """Load tools from the MCP server.

        If a session is provided, it will be used and NOT closed here. This
        ensures the tools remain bound to a live session during agent
        execution, enabling multi-step tool use.
        """
        try:
            if session is not None:
                tools = await load_mcp_tools(session)
                logger.info(
                    f"Loaded {len(tools)} tools from MCP server "
                    f"(bound to active session)"
                )
                return tools

            # Fallback: create a short-lived session (not preferred for agent runs)
            client = await self._initialize_mcp_client()
            async with client.session("bible-study") as temp_session:
                tools = await load_mcp_tools(temp_session)
                logger.info(f"Loaded {len(tools)} tools from MCP server (temp session)")
                return tools

        except Exception as e:
            logger.error(f"Failed to load MCP tools: {e}")
            # Return empty list as fallback
            return []

    async def _get_mcp_system_prompt(self, session: Any | None = None) -> str:
        """Get the system prompt from MCP server.

        If a session is provided, it will be used and NOT closed here. This
        allows prompt retrieval to share the same session as tool loading.
        """
        try:
            # Prefer cached prompt
            if self._system_prompt is not None:
                return self._system_prompt

            async def _load_from_session(active_session: Any) -> str | None:
                prompts_result = await active_session.list_prompts()
                for prompt_info in prompts_result.prompts:
                    if prompt_info.name == "bible_study_method":
                        prompt_result = await active_session.get_prompt(
                            name="bible_study_method", arguments={}
                        )
                        if prompt_result.messages:
                            first_message = prompt_result.messages[0]
                            if hasattr(first_message, "content"):
                                content = first_message.content
                                text_content = getattr(content, "text", None)
                                if text_content:
                                    return str(text_content)
                                if isinstance(content, str):
                                    return content
                                try:
                                    mime_type = getattr(content, "mimeType", None)
                                    data = getattr(content, "data", None)
                                    if mime_type and data:
                                        if str(mime_type).startswith("text/"):
                                            return str(data)
                                        logger.warning(
                                            "Unsupported content type: %s",
                                            mime_type,
                                        )
                                    else:
                                        return str(content)
                                except Exception as inner_e:  # noqa: F841
                                    return str(content)
                return None

            if session is not None:
                prompt_text = await _load_from_session(session)
            else:
                client = await self._initialize_mcp_client()
                async with client.session("bible-study") as temp_session:
                    prompt_text = await _load_from_session(temp_session)

            if prompt_text:
                # Encourage explicit MCP tool usage when needed
                self._system_prompt = (
                    f"{prompt_text}\n\n"
                    "When users ask about specific classes or lessons, use the MCP "
                    "tools to gather details. Prefer calling the "
                    "'retrieve_class_content' tool with the appropriate "
                    "'class_number' (1-10) to read full content, and "
                    "'get_class_summary' or 'list_available_classes' as needed. "
                    "You may perform multiple tool calls over multiple steps "
                    "before answering."
                )
                logger.info("Successfully loaded system prompt from MCP server")
                return self._system_prompt

        except Exception as e:
            logger.warning(f"Failed to get system prompt from MCP server: {e}")

        # Fallback to settings or default
        base_prompt = (
            settings.system_message
            if settings.system_message
            else (
                "You are a Bible study assistant specialized in the "
                "三三制 (Triple-Three Method) approach. Use the available tools "
                "to help users with their Bible study questions and provide "
                "guidance based on the method's principles."
            )
        )
        return (
            f"{base_prompt}\n\n"
            "When users ask about specific classes or lessons, use the MCP tools "
            "to gather details. Prefer calling the 'retrieve_class_content' tool "
            "with the appropriate 'class_number' (1-10) to read full content, "
            "and 'get_class_summary' or 'list_available_classes' as needed. "
            "You may perform multiple tool calls over multiple steps before "
            "answering."
        )

    async def _get_agent(
        self,
        model_name: str,
        temperature: float | None,
        max_tokens: int | None,
        tools: list | None = None,
        system_prompt: str | None = None,
    ):
        """Create a LangGraph agent with provided MCP tools and prompt.

        Tools and system_prompt should be sourced using an active MCP session
        and passed in so that the session remains open during agent execution.
        """
        try:
            # Get the chat model
            chat_model, _ = get_chat_model(
                model_name=model_name,
                temperature=temperature,
                max_tokens=max_tokens,
            )

            if tools is None or system_prompt is None:
                logger.warning(
                    "Creating agent without bound tools/system_prompt; this may "
                    "limit tool use."
                )

            # Create the agent with MCP tools and prompt/state modifier
            self._agent = create_react_agent(
                model=chat_model,
                tools=tools or [],
                prompt=system_prompt or "",
                checkpointer=self._checkpointer,
            )

            session_bound = tools is not None
            logger.info(
                "Created LangGraph agent with %d MCP tools (session-bound: %s)",
                len(tools or []),
                session_bound,
            )
            return self._agent

        except Exception as e:
            logger.error(f"Error creating agent: {e}")
            raise

    async def _cleanup(self):
        """Clean up MCP client resources."""
        if self._mcp_client is not None:
            try:
                # The MultiServerMCPClient should clean up automatically
                # when sessions are closed via context managers
                self._mcp_client = None
                self._tools = None
                self._system_prompt = None
                logger.debug("Cleaned up MCP client resources")
            except Exception as e:
                logger.warning(f"Error during MCP client cleanup: {e}")

    @staticmethod
    async def generate_chat_completion(
        request: ChatCompletionRequest,
    ) -> ChatCompletionResponse:
        """Generate a chat completion response using the LangGraph agent with MCP
        tools.
        """
        agent_service = AgentService()

        try:
            # Initialize MCP and open a session for this run
            client = await agent_service._initialize_mcp_client()
            async with client.session("bible-study") as session:
                # Load tools and prompt bound to this live session
                tools = await agent_service._load_mcp_tools(session=session)
                system_prompt = await agent_service._get_mcp_system_prompt(
                    session=session
                )

                # Create the agent with session-bound tools
                agent = await agent_service._get_agent(
                    model_name=request.model,
                    temperature=request.temperature,
                    max_tokens=request.max_tokens,
                    tools=tools,
                    system_prompt=system_prompt,
                )

                # Convert messages to the format expected by LangGraph
                messages = [
                    {"role": msg.role.value, "content": msg.content}
                    for msg in request.messages
                ]

                # Generate a thread ID for this conversation
                thread_id = str(uuid.uuid4())
                config = RunnableConfig(configurable={"thread_id": thread_id})

                # Invoke the agent
                result = await agent.ainvoke({"messages": messages}, config=config)

            # Extract the final response
            if result and "messages" in result and result["messages"]:
                last_message = result["messages"][-1]
                if hasattr(last_message, "content"):
                    response_text = last_message.content
                else:
                    response_text = str(last_message)
            else:
                response_text = "I apologize, but I couldn't generate a response."

            choice = ChatCompletionChoice(
                index=0,
                message=Message(
                    role=Role.ASSISTANT,
                    content=response_text,
                ),
                finish_reason="stop",
            )

            # Note: Token usage tracking would require additional implementation
            # for LangGraph agents. For now, we'll use placeholder values.
            usage = Usage(
                prompt_tokens=0,
                completion_tokens=0,
                total_tokens=0,
            )

            return ChatCompletionResponse(
                id=f"chatcmpl-{uuid.uuid4()}",
                model=request.model,
                choices=[choice],
                usage=usage,
            )

        except Exception as e:
            logger.exception(f"Error generating agent chat completion: {e}")
            raise
        finally:
            # Clean up MCP resources
            await agent_service._cleanup()

    @staticmethod
    async def stream_chat_completion(
        request: ChatCompletionRequest,
    ) -> AsyncGenerator[ChatCompletionStreamResponse, None]:
        """Generate a streaming chat completion response using the LangGraph agent
        with MCP tools.
        """
        agent_service = AgentService()

        try:
            # Initialize MCP and open a session for this run
            client = await agent_service._initialize_mcp_client()
            async with client.session("bible-study") as session:
                # Load tools and prompt bound to this live session
                tools = await agent_service._load_mcp_tools(session=session)
                system_prompt = await agent_service._get_mcp_system_prompt(
                    session=session
                )

                # Create the agent with session-bound tools
                agent = await agent_service._get_agent(
                    model_name=request.model,
                    temperature=request.temperature,
                    max_tokens=request.max_tokens,
                    tools=tools,
                    system_prompt=system_prompt,
                )

                # Convert messages to the format expected by LangGraph
                messages = [
                    {"role": msg.role.value, "content": msg.content}
                    for msg in request.messages
                ]

                # Generate unique IDs
                completion_id = f"chatcmpl-{uuid.uuid4()}"
                thread_id = str(uuid.uuid4())
                config = RunnableConfig(configurable={"thread_id": thread_id})

                # Send the initial chunk with role information
                initial_choice = ChatCompletionStreamChoice(
                    index=0,
                    delta=Delta(role=Role.ASSISTANT),
                    finish_reason=None,
                )

                initial_response = ChatCompletionStreamResponse(
                    id=completion_id,
                    model=request.model,
                    choices=[initial_choice],
                )

                yield initial_response

                # Stream the agent's execution
                accumulated_content = ""

                async for chunk in agent.astream({"messages": messages}, config=config):
                    # Extract content from the chunk
                    if isinstance(chunk, dict):
                        for node_name, node_data in chunk.items():
                            if node_name == "agent" and isinstance(node_data, dict):
                                messages_data = node_data.get("messages", [])
                                if messages_data:
                                    last_msg = messages_data[-1]
                                    if hasattr(last_msg, "content"):
                                        new_content = last_msg.content
                                        if (
                                            new_content
                                            and new_content != accumulated_content
                                        ):
                                            # Send the new content as a chunk
                                            content_chunk = new_content[
                                                len(accumulated_content) :
                                            ]
                                            if content_chunk:
                                                choice = ChatCompletionStreamChoice(
                                                    index=0,
                                                    delta=Delta(content=content_chunk),
                                                    finish_reason=None,
                                                )

                                                response = ChatCompletionStreamResponse(
                                                    id=completion_id,
                                                    model=request.model,
                                                    choices=[choice],
                                                )

                                                yield response
                                                accumulated_content = new_content

                # Send the final chunk with finish_reason
                final_choice = ChatCompletionStreamChoice(
                    index=0,
                    delta=Delta(),
                    finish_reason="stop",
                )

                final_response = ChatCompletionStreamResponse(
                    id=completion_id,
                    model=request.model,
                    choices=[final_choice],
                )

                yield final_response

        except Exception as e:
            logger.exception(f"Error generating streaming agent chat completion: {e}")
            raise
        finally:
            # Clean up MCP resources
            await agent_service._cleanup()
