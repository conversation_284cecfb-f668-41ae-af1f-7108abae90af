"""
Main application entry point.
"""

import logging
import time
from logging.handlers import RotatingFileHandler
from pathlib import Path

from fastapi import FastAP<PERSON>, status
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse

from chat.api import router
from chat.config import settings

project_root = Path(__file__).parent.parent.parent
log_dir = project_root / "log"
log_dir.mkdir(exist_ok=True)

log_filename = f"chatbot_{time.strftime('%Y%m%d')}.log"
log_filepath = log_dir / log_filename

log_level = logging.DEBUG if settings.debug else logging.INFO
log_format = logging.Formatter("%(asctime)s - %(name)s - %(levelname)s - %(message)s")

file_handler = RotatingFileHandler(
    filename=log_filepath,
    maxBytes=10 * 1024 * 1024,
    backupCount=5,
    encoding="utf-8",
)
file_handler.setLevel(log_level)
file_handler.setFormatter(log_format)

console_handler = logging.StreamHandler()
console_handler.setLevel(log_level)
console_handler.setFormatter(log_format)

root_logger = logging.getLogger()

for handler in root_logger.handlers[:]:
    root_logger.removeHandler(handler)

root_logger.setLevel(log_level)
root_logger.addHandler(file_handler)
root_logger.addHandler(console_handler)

# Create module logger
logger = logging.getLogger(__name__)


def create_app() -> FastAPI:
    """Create the FastAPI application."""
    app = FastAPI(
        title=settings.api_title,
        description=settings.api_description,
        version=settings.api_version,
        docs_url=f"{settings.api_prefix}/docs",
        redoc_url=f"{settings.api_prefix}/redoc",
        openapi_url=f"{settings.api_prefix}/openapi.json",
    )

    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )

    @app.exception_handler(Exception)
    async def _(_, exc: Exception) -> JSONResponse:
        logger.exception(f"Unhandled exception: {exc}")
        return JSONResponse(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            content={"error": {"message": str(exc), "type": "internal_server_error"}},
        )

    app.include_router(router)

    return app


app = create_app()


# if __name__ == "__main__":
#     port = int(os.environ.get("PORT", 8080))
#     logger.info(f"Starting application on port {port}")

#     uvicorn.run(
#         "chat.main:app",
#         host="0.0.0.0",
#         port=port,
#         reload=settings.debug,
#         log_config=None,
#     )
