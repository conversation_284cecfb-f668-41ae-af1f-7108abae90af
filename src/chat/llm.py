"""
LLM integration with OpenRouter and LangChain.
"""

import logging
import os
from collections.abc import As<PERSON><PERSON>enerator
from typing import Any

from langchain_core.callbacks import BaseCallbackHandler
from langchain_core.language_models.chat_models import BaseChatModel
from langchain_core.messages import AIMessage, BaseMessage, HumanMessage, SystemMessage
from langchain_openai import ChatOpenAI

from bible_study_mcp.context import ContextLoader
from chat.config import settings

logger = logging.getLogger(__name__)


class TokenUsageCallbackHandler(BaseCallbackHandler):
    """Tracks token usage."""

    def __init__(self) -> None:
        super().__init__()
        self.prompt_tokens = 0
        self.completion_tokens = 0
        self.total_tokens = 0

    def on_llm_end(self, response: Any, **_: Any) -> None:
        if hasattr(response, "llm_output") and response.llm_output:
            if "token_usage" in response.llm_output:
                token_usage = response.llm_output["token_usage"]
                self.prompt_tokens = token_usage.get("prompt_tokens", 0)
                self.completion_tokens = token_usage.get("completion_tokens", 0)
                self.total_tokens = token_usage.get("total_tokens", 0)


def get_chat_model(
    model_name: str | None = None,
    temperature: float | None = None,
    max_tokens: int | None = None,
) -> tuple[BaseChatModel, TokenUsageCallbackHandler]:
    """Get a chat model from OpenRouter.

    Args:
        model_name: Name of the model to use. Can be a shorthand name from
            available_models or a raw model name
            (e.g., 'anthropic/claude-3-opus-20240229').
        temperature: Temperature for sampling. Higher values mean more randomness.
        max_tokens: Maximum number of tokens to generate.

    Returns:
        A tuple containing the chat model and a token usage handler.
    """
    model_name = model_name or settings.default_model
    temperature = temperature if temperature is not None else settings.temperature
    max_tokens = max_tokens or settings.max_tokens

    # Check if the model_name is a key in available_models, otherwise use it directly
    # This allows passing raw model names directly
    # (e.g., 'anthropic/claude-3-opus-20240229')
    model_id = settings.available_models.get(model_name, model_name)

    # Log the model being used
    logger.info(f"Using model: {model_id} (requested: {model_name})")

    token_usage_handler = TokenUsageCallbackHandler()

    # Set environment variable for OpenAI client
    os.environ["OPENAI_API_KEY"] = os.environ.get(
        "OPENROUTER_API_KEY", settings.openrouter_api_key
    )

    # Create the chat model with appropriate parameters
    chat_model = ChatOpenAI(
        base_url=settings.openrouter_base_url,
        model=model_id,
        temperature=temperature,
        max_completion_tokens=max_tokens,  # Use max_completion_tokens parameter
        callbacks=[token_usage_handler],
    )

    return chat_model, token_usage_handler


async def stream_chat_model(
    model_name: str | None = None,
    temperature: float | None = None,
    max_tokens: int | None = None,
    messages: list[BaseMessage] | None = None,
) -> AsyncGenerator[str | list[str | dict[str, Any]], None]:
    """Stream chat completion from OpenRouter.

    Args:
        model_name: Name of the model to use. Can be a shorthand name from
            available_models or a raw model name
            (e.g., 'anthropic/claude-3-opus-20240229').
        temperature: Temperature for sampling. Higher values mean more randomness.
        max_tokens: Maximum number of tokens to generate.
        messages: List of LangChain messages to send to the model.

    Yields:
        Content chunks as they are generated by the model.

    Raises:
        ValueError: If messages is None.
    """
    if messages is None:
        raise ValueError("messages parameter cannot be None")

    model_name = model_name or settings.default_model
    temperature = temperature if temperature is not None else settings.temperature
    max_tokens = max_tokens or settings.max_tokens

    # Check if the model_name is a key in available_models, otherwise use it directly
    model_id = settings.available_models.get(model_name, model_name)

    # Log the model being used
    logger.info(f"Streaming with model: {model_id} (requested: {model_name})")

    # Set environment variable for OpenAI client
    os.environ["OPENAI_API_KEY"] = os.environ.get(
        "OPENROUTER_API_KEY", settings.openrouter_api_key
    )

    # Create the chat model with streaming enabled
    chat_model = ChatOpenAI(
        base_url=settings.openrouter_base_url,
        model=model_id,
        temperature=temperature,
        max_completion_tokens=max_tokens,
        streaming=True,  # Enable streaming
    )

    # Stream the response
    async for chunk in chat_model.astream(messages):
        if hasattr(chunk, "content") and chunk.content:
            yield chunk.content


def convert_message_to_langchain(message: dict[str, str]) -> BaseMessage:
    """Convert a message dict to a LangChain message."""
    role = message["role"]
    content = message["content"]

    if role == "system":
        return SystemMessage(content=content)
    elif role == "user":
        return HumanMessage(content=content)
    elif role == "assistant":
        return AIMessage(content=content)
    else:
        raise ValueError(f"Unknown role: {role}")


async def get_mcp_system_message() -> str:
    """Get system message from MCP server, with fallback to settings."""
    try:
        context_loader = ContextLoader()
        overview_content = await context_loader.load_method_overview()

        # Create the same prompt as the MCP server's bible_study_method()
        return (
            f"You are a Bible study assistant specialized in the "
            f"三三制 (Triple-Three Method) approach.\n\n"
            f"Here is the complete method overview:\n\n"
            f"{overview_content}\n\n"
            f"Use this method to guide users in their Bible study, "
            f"helping them apply the nine-point framework: "
            f"针对 (Target), 绝对 (Absolute), and 比对 (Compare), "
            f"each with three sub-points. Always reference the specific "
            f"principles and techniques outlined in this method when "
            f"providing Bible study guidance."
        )
    except Exception as e:
        logger.warning(f"Could not load MCP system message, using fallback: {e}")
        return settings.system_message


def create_langchain_messages(messages: list[dict[str, str]]) -> list[BaseMessage]:
    """Convert message dicts to LangChain messages."""
    has_system_message = any(msg["role"] == "system" for msg in messages)
    langchain_messages = []

    if not has_system_message:
        langchain_messages.append(SystemMessage(content=settings.system_message))

    for message in messages:
        langchain_messages.append(convert_message_to_langchain(message))

    return langchain_messages


async def create_langchain_messages_async(
    messages: list[dict[str, str]],
) -> list[BaseMessage]:
    """Convert message dicts to LangChain messages with MCP system prompt."""
    has_system_message = any(msg["role"] == "system" for msg in messages)
    langchain_messages = []

    if not has_system_message:
        # Try to get MCP system message first, fallback to settings
        system_content = await get_mcp_system_message()
        langchain_messages.append(SystemMessage(content=system_content))

    for message in messages:
        langchain_messages.append(convert_message_to_langchain(message))

    return langchain_messages


def get_available_models() -> dict[str, dict[str, Any]]:
    """Get a dictionary of available models with their details.

    Returns:
        A dictionary where keys are model shorthand names and values are dictionaries
        containing model details such as the full model ID and provider.
    """
    model_details = {}

    for shorthand, model_id in settings.available_models.items():
        # Extract provider from model_id
        # (e.g., "anthropic" from "anthropic/claude-3-opus-20240229")
        provider = model_id.split("/")[0] if "/" in model_id else "unknown"

        # Create model details
        model_details[shorthand] = {
            "id": model_id,
            "provider": provider,
            "shorthand": shorthand,
            "supports_streaming": True,  # All models support streaming
            # through OpenRouter
        }

    return model_details
