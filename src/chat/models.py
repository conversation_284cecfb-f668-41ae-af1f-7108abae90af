"""
Pydantic models for the chatbot API.

These models follow the OpenAI API format for compatibility.
"""

from datetime import datetime
from enum import Enum

from pydantic import BaseModel, Field


class Role(str, Enum):
    """Message role types."""

    SYSTEM = "system"
    USER = "user"
    ASSISTANT = "assistant"
    FUNCTION = "function"


class Message(BaseModel):
    """Conversation message."""

    role: Role
    content: str
    name: str | None = None


class ChatCompletionRequest(BaseModel):
    """Chat completion request (OpenAI format)."""

    model: str
    messages: list[Message]
    temperature: float | None = 0.7
    top_p: float | None = 1.0
    n: int | None = 1
    stream: bool | None = False
    max_tokens: int | None = None
    presence_penalty: float | None = 0
    frequency_penalty: float | None = 0
    user: str | None = None


class ChatCompletionChoice(BaseModel):
    """Chat completion choice."""

    index: int
    message: Message
    finish_reason: str | None = None


class Usage(BaseModel):
    """Token usage stats."""

    prompt_tokens: int
    completion_tokens: int
    total_tokens: int


class ChatCompletionResponse(BaseModel):
    """Chat completion response (OpenAI format)."""

    id: str
    object: str = "chat.completion"
    created: int = Field(default_factory=lambda: int(datetime.now().timestamp()))
    model: str
    choices: list[ChatCompletionChoice]
    usage: Usage


class ErrorResponse(BaseModel):
    """API error response."""

    error: dict[str, str]


class HealthResponse(BaseModel):
    """Health check response."""

    status: str = "ok"
    version: str


# Streaming response models
class Delta(BaseModel):
    """Delta content for streaming responses."""

    role: Role | None = None
    content: str | None = None


class ChatCompletionStreamChoice(BaseModel):
    """Chat completion choice for streaming responses."""

    index: int
    delta: Delta
    finish_reason: str | None = None


class ChatCompletionStreamResponse(BaseModel):
    """Chat completion streaming response (OpenAI format)."""

    id: str
    object: str = "chat.completion.chunk"
    created: int = Field(default_factory=lambda: int(datetime.now().timestamp()))
    model: str
    choices: list[ChatCompletionStreamChoice]
