"""
Configuration settings for the chatbot service.
"""

import json
import logging
from pathlib import Path

from dotenv import load_dotenv
from pydantic import Field, field_validator
from pydantic_settings import BaseSettings, SettingsConfigDict

logger = logging.getLogger(__name__)

env_path = Path(__file__).parent.parent.parent / ".env"
load_dotenv(dotenv_path=env_path)


class Settings(BaseSettings):
    """Application settings."""

    api_title: str = "RHCC Chatbot API"
    api_description: str = "API for chatting with RHCC's LLM service"
    api_version: str = "v1"
    api_prefix: str = "/api/v1"
    debug: bool = False

    api_keys: list[str] = Field(default_factory=list)

    @field_validator("api_keys", mode="before")
    @classmethod
    def validate_api_keys(cls, v):
        """Validate and parse API keys."""
        if isinstance(v, list):
            return v

        if not v or not isinstance(v, str):
            logger.warning("API_KEYS is empty or not a string. Using empty list.")
            return []

        try:
            # Try to parse as JSON
            return json.loads(v)
        except json.JSONDecodeError as e:
            # If JSON parsing fails, log the error and return an empty list
            # SECURITY: Do not log the actual value as it may contain sensitive API keys
            logger.error(
                f"Failed to parse API_KEYS as JSON: {e}. "
                f"Value length: {len(str(v)) if v else 0}"
            )
            logger.error("Using empty list for API_KEYS. Authentication will fail.")
            return []

    openrouter_api_key: str = ""
    openrouter_base_url: str = "https://openrouter.ai/api/v1"
    default_model: str = "gpt-4.1"
    available_models: dict[str, str] = Field(
        default_factory=lambda: {
            "claude-3.5-sonnet": "anthropic/claude-3.5-sonnet-20240620",
            "gpt-4o": "openai/gpt-4o-2024-11-20",
            "gpt-4.1": "openai/gpt-4.1",
            "gemini-2.5-pro": "google/gemini-2.5-pro-preview",
            "deepseek-v3": "deepseek/deepseek-chat-v3-0324",
        }
    )

    system_message: str = """You are a helpful AI assistant."""
    max_tokens: int = 4000
    temperature: float = 0.7

    rate_limit_requests: int = 100
    rate_limit_period: int = 60

    # Agent settings
    use_agent: bool = False
    agent_mcp_server_path: str = ""

    # Logging settings
    log_file: str = ""  # If set, logs will be written to this file
    log_level: str = "INFO"

    model_config = SettingsConfigDict(
        env_file=".env", env_file_encoding="utf-8", extra="ignore"
    )


settings = Settings()
