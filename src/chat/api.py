"""
FastAPI routes for the chatbot API.
"""

import json
import logging

from fastapi import <PERSON><PERSON>out<PERSON>, Depends, HTTPException, Request, status
from fastapi.responses import StreamingResponse
from fastapi.security import HTT<PERSON><PERSON>earer
from pydantic import BaseModel, Field

from chat.config import settings
from chat.llm import get_available_models
from chat.models import (
    ChatCompletionRequest,
    ChatCompletionResponse,
    ErrorResponse,
    HealthResponse,
)
from chat.service import ChatbotService

logger = logging.getLogger(__name__)


# Define model-related Pydantic models
class ModelProvider(BaseModel):
    """Model provider information."""

    name: str = Field(..., description="Provider name (e.g., 'anthropic', 'openai')")


class ModelDetails(BaseModel):
    """Details about an available model."""

    id: str = Field(..., description="Full model ID used by the provider")
    provider: str = Field(..., description="Model provider name")
    shorthand: str = Field(..., description="Shorthand name for the model")
    supports_streaming: bool = Field(
        True, description="Whether the model supports streaming"
    )


class ModelsResponse(BaseModel):
    """Response containing available models."""

    models: dict[str, ModelDetails] = Field(
        ..., description="Dictionary of available models"
    )


router = APIRouter(prefix=settings.api_prefix)

# Bearer token security
security = HTTPBearer(auto_error=False)


async def get_api_key(request: Request) -> str:
    """Validate the API key using Bearer authentication with strict validation."""
    if not settings.api_keys:
        # Development mode - no authentication required
        auth_header = request.headers.get("authorization", "")
        if auth_header.startswith("Bearer "):
            return auth_header[7:]  # Return token after "Bearer "
        return ""

    # Get the Authorization header
    auth_header = request.headers.get("authorization")
    if not auth_header:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid or missing API key",
        )

    # Strict validation: must start with exactly "Bearer " (case-sensitive)
    if not auth_header.startswith("Bearer "):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid or missing API key",
        )

    # Extract the token
    token = auth_header[7:]  # Remove "Bearer " prefix

    # Validate token format
    if not token or not token.strip():
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid or missing API key",
        )

    # Check for suspicious characters
    if "\x00" in token or len(token) != len(token.strip()):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid or missing API key",
        )

    # Check if token contains spaces (indicating malformed header
    # like "Bearer token extra")
    if " " in token:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid or missing API key",
        )

    # Validate against configured API keys
    if token not in settings.api_keys:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid or missing API key",
        )

    return token


@router.get(
    "/health",
    response_model=HealthResponse,
    summary="Health check",
    description="Check if the API is running",
)
async def health() -> HealthResponse:
    """Health check endpoint."""
    return HealthResponse(version=settings.api_version)


@router.get(
    "/models",
    response_model=ModelsResponse,
    summary="List available models",
    description="Get a list of all available models with their details",
)
async def list_models(_: str = Depends(get_api_key)) -> ModelsResponse:
    """List all available models endpoint.

    Returns:
        A dictionary of available models with their details.
    """
    raw_models = get_available_models()

    # Convert the raw dictionary to a dictionary of ModelDetails objects
    models: dict[str, ModelDetails] = {}
    for key, details in raw_models.items():
        models[key] = ModelDetails(
            id=details["id"],
            provider=details["provider"],
            shorthand=details["shorthand"],
            supports_streaming=details["supports_streaming"],
        )

    return ModelsResponse(models=models)


@router.post(
    "/chat/completions",
    response_model=None,  # Disable automatic response model generation
    responses={
        200: {
            "description": "Chat completion response",
            "content": {
                "application/json": {"model": ChatCompletionResponse},
                "text/event-stream": {"example": "data: {...}\n\ndata: [DONE]\n\n"},
            },
        },
        401: {"model": ErrorResponse},
        422: {"model": ErrorResponse},
        500: {"model": ErrorResponse},
    },
    summary="Create a chat completion",
    description="Create a completion for the chat message. "
    "Supports both streaming and non-streaming responses.",
)
async def create_chat_completion(
    request: ChatCompletionRequest,
    _: str = Depends(get_api_key),
):
    """Create a chat completion."""
    try:
        # Check if the model is valid - it can be either a shorthand name
        # or a raw model name
        # A raw model name should contain a provider prefix
        # (e.g., "anthropic/claude-3-opus-20240229")
        is_valid_model = (
            request.model in settings.available_models  # Shorthand name
            or request.model in settings.available_models.values()  # Full model ID
            or "/" in request.model  # Likely a raw model name with provider prefix
        )

        if not is_valid_model:
            # Get a list of available models to show in the error message
            available_models = list(settings.available_models.keys())
            # Also include a few examples of raw model names
            example_raw_models = [
                next(
                    iter(settings.available_models.values()), ""
                ),  # First raw model name
                "anthropic/claude-3-opus-20240229",  # Example raw model name
                "openai/gpt-4-turbo",  # Example raw model name
            ]

            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=(
                    f"Model '{request.model}' is not recognized. \n"
                    f"Available shorthand models: {available_models}\n"
                    f"Or use raw model names like: {example_raw_models}\n"
                    f"Use the /models endpoint to see all available models."
                ),
            )

        # Log the model being used
        logger.info(f"Using model: {request.model} (streaming: {request.stream})")

        # Handle streaming vs non-streaming requests
        if request.stream:
            # Return streaming response
            async def generate_stream():
                try:
                    async for chunk in ChatbotService.stream_chat_completion(request):
                        # Format as Server-Sent Events
                        chunk_json = chunk.model_dump_json()
                        yield f"data: {chunk_json}\n\n"

                    # Send the final [DONE] message
                    yield "data: [DONE]\n\n"
                except Exception as e:
                    logger.exception(f"Error in streaming: {e}")
                    # Send error in SSE format
                    error_data = {
                        "error": {"message": str(e), "type": "internal_server_error"}
                    }
                    yield f"data: {json.dumps(error_data)}\n\n"

            return StreamingResponse(
                generate_stream(),
                media_type="text/event-stream",
                headers={
                    "Cache-Control": "no-cache",
                    "Connection": "keep-alive",
                    "X-Accel-Buffering": "no",  # Disable nginx buffering
                },
            )
        else:
            # Return regular response
            response = await ChatbotService.generate_chat_completion(request)
            return response
    except HTTPException:
        raise
    except Exception as e:
        logger.exception(f"Error creating chat completion: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error creating chat completion: {str(e)}",
        )
